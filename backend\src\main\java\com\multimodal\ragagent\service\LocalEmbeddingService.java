package com.multimodal.ragagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

@Service
public class LocalEmbeddingService {
    
    private static final Logger logger = LoggerFactory.getLogger(LocalEmbeddingService.class);
    
    @Value("${embedding.service.host:localhost}")
    private String embeddingServiceHost;
    
    @Value("${embedding.service.port:8001}")
    private int embeddingServicePort;
    
    @Value("${embedding.model:sentence-transformers/all-MiniLM-L6-v2}")
    private String embeddingModel;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();
    private final Random random = new Random();
    
    public List<Double> generateEmbedding(String text) {
        try {
            // First try to use local embedding service if available
            if (isLocalServiceAvailable()) {
                return callLocalEmbeddingService(text);
            } else {
                // Fallback to mock embeddings for development/testing
                logger.warn("Local embedding service not available, using mock embeddings");
                return generateMockEmbedding(text);
            }
        } catch (Exception e) {
            logger.warn("Failed to generate embedding, using mock: {}", e.getMessage());
            return generateMockEmbedding(text);
        }
    }
    
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        List<List<Double>> embeddings = new ArrayList<>();
        
        try {
            if (isLocalServiceAvailable()) {
                return callLocalEmbeddingServiceBatch(texts);
            } else {
                // Fallback to mock embeddings
                logger.warn("Local embedding service not available, using mock embeddings");
                for (String text : texts) {
                    embeddings.add(generateMockEmbedding(text));
                }
                return embeddings;
            }
        } catch (Exception e) {
            logger.warn("Failed to generate embeddings, using mock: {}", e.getMessage());
            for (String text : texts) {
                embeddings.add(generateMockEmbedding(text));
            }
            return embeddings;
        }
    }
    
    private List<Double> callLocalEmbeddingService(String text) throws IOException {
        String url = String.format("http://%s:%d/embed", embeddingServiceHost, embeddingServicePort);
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("text", text);
        requestBody.put("model", embeddingModel);
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        return httpClient.execute(request, response -> {
            int statusCode = response.getCode();
            if (statusCode == 200) {
                String responseBody = new String(response.getEntity().getContent().readAllBytes());
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                List<Double> embedding = new ArrayList<>();
                JsonNode embeddingArray = jsonResponse.get("embedding");
                for (JsonNode value : embeddingArray) {
                    embedding.add(value.asDouble());
                }
                
                logger.debug("Generated embedding with {} dimensions", embedding.size());
                return embedding;
            } else {
                throw new IOException("Embedding service returned status: " + statusCode);
            }
        });
    }
    
    private List<List<Double>> callLocalEmbeddingServiceBatch(List<String> texts) throws IOException {
        String url = String.format("http://%s:%d/embed_batch", embeddingServiceHost, embeddingServicePort);
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        ArrayNode textsArray = objectMapper.createArrayNode();
        texts.forEach(textsArray::add);
        requestBody.set("texts", textsArray);
        requestBody.put("model", embeddingModel);
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        return httpClient.execute(request, response -> {
            int statusCode = response.getCode();
            if (statusCode == 200) {
                String responseBody = new String(response.getEntity().getContent().readAllBytes());
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                List<List<Double>> embeddings = new ArrayList<>();
                JsonNode embeddingsArray = jsonResponse.get("embeddings");
                
                for (JsonNode embeddingNode : embeddingsArray) {
                    List<Double> embedding = new ArrayList<>();
                    for (JsonNode value : embeddingNode) {
                        embedding.add(value.asDouble());
                    }
                    embeddings.add(embedding);
                }
                
                logger.debug("Generated {} embeddings with {} dimensions each", 
                           embeddings.size(), embeddings.get(0).size());
                return embeddings;
            } else {
                throw new IOException("Embedding service returned status: " + statusCode);
            }
        });
    }
    
    private boolean isLocalServiceAvailable() {
        try {
            String url = String.format("http://%s:%d/health", embeddingServiceHost, embeddingServicePort);
            HttpPost request = new HttpPost(url);
            
            return httpClient.execute(request, response -> {
                return response.getCode() == 200;
            });
        } catch (Exception e) {
            logger.debug("Local embedding service not available: {}", e.getMessage());
            return false;
        }
    }
    
    /**
     * Generate mock embeddings for development/testing purposes
     * These are deterministic based on text content but not semantically meaningful
     */
    private List<Double> generateMockEmbedding(String text) {
        // Create a deterministic but pseudo-random embedding based on text hash
        int hash = text.hashCode();
        Random textRandom = new Random(hash);
        
        List<Double> embedding = new ArrayList<>();
        int dimensions = 384; // Same as all-MiniLM-L6-v2
        
        // Generate normalized random vector
        double[] values = new double[dimensions];
        double norm = 0.0;
        
        for (int i = 0; i < dimensions; i++) {
            values[i] = textRandom.nextGaussian();
            norm += values[i] * values[i];
        }
        
        // Normalize the vector
        norm = Math.sqrt(norm);
        for (int i = 0; i < dimensions; i++) {
            embedding.add(values[i] / norm);
        }
        
        return embedding;
    }
    
    public boolean isConfigured() {
        return isLocalServiceAvailable();
    }
    
    public String getEmbeddingModel() {
        return embeddingModel;
    }
    
    public int getEmbeddingDimensions() {
        // all-MiniLM-L6-v2 has 384 dimensions
        if (embeddingModel.contains("all-MiniLM-L6-v2")) {
            return 384;
        }
        // Default to 384 for most sentence-transformer models
        return 384;
    }
    
    public String getServiceStatus() {
        if (isLocalServiceAvailable()) {
            return "Local embedding service available";
        } else {
            return "Using mock embeddings (local service not available)";
        }
    }
}
