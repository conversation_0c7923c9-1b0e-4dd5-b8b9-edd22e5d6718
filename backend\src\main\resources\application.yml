server:
  port: ${SERVER_PORT:8081}

spring:
  application:
    name: multimodal-rag-agent
  
  datasource:
    url: jdbc:postgresql://${DB_HOST:localhost}:${DB_PORT:5433}/${DB_NAME:multimodal_rag_dev}
    username: ${DB_USERNAME:postgres}
    password: ${DB_PASSWORD:password}
    driver-class-name: org.postgresql.Driver
  
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.PostgreSQLDialect
        format_sql: true
  
  servlet:
    multipart:
      max-file-size: ${MAX_FILE_SIZE:50MB}
      max-request-size: ${MAX_FILE_SIZE:50MB}
  
  security:
    user:
      name: admin
      password: admin

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:your-super-secret-jwt-key-change-this-in-production}
  expiration: ${JWT_EXPIRATION:86400000}

# Groq API Configuration (removed duplicate - see below for full config)

# Vector Database Configuration
vector:
  db:
    type: ${VECTOR_DB_TYPE:chroma}
    host: ${VECTOR_DB_HOST:localhost}
    port: ${VECTOR_DB_PORT:8000}
  chroma:
    collection-name: ${CHROMA_COLLECTION:document_embeddings}
    distance-function: ${CHROMA_DISTANCE:cosine}

# File Upload Configuration
file:
  upload:
    dir: ${UPLOAD_DIR:./uploads}
    allowed-types: ${ALLOWED_FILE_TYPES:pdf,ppt,pptx,doc,docx,csv,txt}

# OCR Configuration
ocr:
  tesseract:
    path: ${TESSERACT_PATH:/usr/bin/tesseract}
    language: ${OCR_LANGUAGE:eng}

# Embedding Configuration - Hugging Face (FREE)
embedding:
  provider: ${EMBEDDING_PROVIDER:huggingface}  # Use Hugging Face for free embeddings
  model: ${EMBEDDING_MODEL:sentence-transformers/all-MiniLM-L6-v2}
  image-model: ${IMAGE_EMBEDDING_MODEL:sentence-transformers/clip-ViT-B-32}
  service:
    host: ${EMBEDDING_SERVICE_HOST:localhost}
    port: ${EMBEDDING_SERVICE_PORT:8001}

# Hugging Face Configuration (FREE)
huggingface:
  api:
    key: ${HUGGINGFACE_API_KEY:your-huggingface-api-key}
    url: ${HUGGINGFACE_API_URL:https://api-inference.huggingface.co/models}
  model: ${HUGGINGFACE_MODEL:sentence-transformers/all-MiniLM-L6-v2}

# Groq Configuration for LLM (fast and cost-effective)
groq:
  api:
    key: ${GROQ_API_KEY:}
    base-url: ${GROQ_BASE_URL:https://api.groq.com/openai/v1}
  model:
    default: ${GROQ_MODEL:llama3-8b-8192}
    fast: ${GROQ_FAST_MODEL:llama3-8b-8192}
    quality: ${GROQ_QUALITY_MODEL:llama3-70b-8192}
  settings:
    max-tokens: ${GROQ_MAX_TOKENS:2048}
    temperature: ${GROQ_TEMPERATURE:0.7}
    timeout-seconds: ${GROQ_TIMEOUT:30}

# RAG Configuration
rag:
  max-context-length: ${MAX_CONTEXT_LENGTH:4000}
  chunk-size: ${CHUNK_SIZE:1000}
  chunk-overlap: ${CHUNK_OVERLAP:200}

# CORS Configuration
cors:
  allowed-origins: ${FRONTEND_URL:http://localhost:3000}

# Logging
logging:
  level:
    com.multimodal.ragagent: ${LOG_LEVEL:INFO}
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/application.log

# Actuator
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: when-authorized
