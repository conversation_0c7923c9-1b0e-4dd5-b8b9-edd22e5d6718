package com.multimodal.ragagent.repository;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentRepository extends JpaRepository<Document, Long> {
    
    List<Document> findByUser(User user);
    
    Page<Document> findByUser(User user, Pageable pageable);
    
    List<Document> findByUserAndProcessingStatus(User user, Document.ProcessingStatus status);
    
    Optional<Document> findByIdAndUser(Long id, User user);
    
    @Query("SELECT d FROM Document d WHERE d.user = :user AND d.originalFilename LIKE %:filename%")
    List<Document> findByUserAndFilenameContaining(@Param("user") User user, @Param("filename") String filename);
    
    @Query("SELECT d FROM Document d WHERE d.user = :user AND d.fileType = :fileType")
    List<Document> findByUserAndFileType(@Param("user") User user, @Param("fileType") Document.FileType fileType);
    
    long countByUser(User user);
    
    long countByUserAndProcessingStatus(User user, Document.ProcessingStatus status);
}
