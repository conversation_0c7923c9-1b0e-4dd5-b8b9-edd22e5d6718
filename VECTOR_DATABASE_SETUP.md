# Vector Database Setup Guide

This guide explains how to set up the vector database (ChromaDB) for the RAG functionality.

## Prerequisites

- <PERSON><PERSON> and <PERSON><PERSON> Compose installed
- OpenAI API key for embeddings

## Quick Start

### 1. Start ChromaDB

```bash
# Using the provided Docker Compose file
docker-compose -f docker-compose.chromadb.yml up -d

# Or using Docker directly
docker run -p 8000:8000 chromadb/chroma
```

### 2. Set OpenAI API Key

```bash
# Linux/Mac
export OPENAI_API_KEY=your-openai-api-key-here

# Windows
set OPENAI_API_KEY=your-openai-api-key-here
```

### 3. Start the Application

The application will automatically:
- Connect to ChromaDB on startup
- Create the necessary collection
- Index uploaded documents
- Enable RAG queries

## Configuration

### Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `VECTOR_DB_HOST` | `localhost` | ChromaDB host |
| `VECTOR_DB_PORT` | `8000` | ChromaDB port |
| `CHROMA_COLLECTION` | `document_embeddings` | Collection name |
| `OPENAI_API_KEY` | - | OpenAI API key for embeddings |
| `OPENAI_EMBEDDING_MODEL` | `text-embedding-ada-002` | Embedding model |

### Application Properties

```yaml
# Vector Database Configuration
vector:
  db:
    type: chroma
    host: localhost
    port: 8000
  chroma:
    collection-name: document_embeddings
    distance-function: cosine

# OpenAI Configuration for Embeddings
openai:
  api:
    key: ${OPENAI_API_KEY:your-openai-api-key-here}
  embedding:
    model: text-embedding-ada-002
```

## Features Enabled

Once the vector database is set up, the following features become available:

### 1. Automatic Document Indexing
- Documents are automatically indexed after processing
- Text chunks are converted to embeddings
- Stored in ChromaDB for fast similarity search

### 2. RAG Queries
- `/api/rag/query` - Query specific document
- `/api/rag/query-all` - Query across all user documents
- `/api/rag/chat` - Chat with document

### 3. Vector Search
- Semantic similarity search
- Relevant chunk retrieval
- Context assembly for LLM

## API Endpoints

### Query Document
```bash
POST /api/rag/query
{
  "documentId": 1,
  "question": "What is the main topic of this document?"
}
```

### Query All Documents
```bash
POST /api/rag/query-all
{
  "question": "Find information about machine learning"
}
```

### Chat with Document
```bash
POST /api/rag/chat
{
  "documentId": 1,
  "messages": [
    {"role": "user", "content": "Explain the key concepts"}
  ]
}
```

### Check RAG Status
```bash
GET /api/rag/status
```

## Troubleshooting

### ChromaDB Not Available
- Check if ChromaDB is running: `docker ps`
- Verify port 8000 is not in use
- Check logs: `docker logs <chromadb-container>`

### OpenAI API Issues
- Verify API key is set correctly
- Check API key permissions
- Monitor usage limits

### No Search Results
- Ensure documents are processed and indexed
- Check vector database status endpoint
- Verify embeddings are generated

## Performance Considerations

### Embedding Costs
- OpenAI charges per token for embeddings
- Typical document: $0.0001 per 1K tokens
- Consider batch processing for large documents

### ChromaDB Performance
- Default in-memory storage
- For production, use persistent storage
- Consider scaling for large document collections

## Production Deployment

### Persistent Storage
```yaml
services:
  chromadb:
    image: chromadb/chroma:latest
    volumes:
      - ./chroma_data:/chroma/chroma
    environment:
      - PERSIST_DIRECTORY=/chroma/chroma
```

### Security
- Use authentication for ChromaDB in production
- Secure OpenAI API key storage
- Network security for ChromaDB access

## Next Steps

1. **Groq Integration**: Replace basic responses with Groq API
2. **Advanced Chunking**: Implement semantic chunking
3. **Hybrid Search**: Combine vector and keyword search
4. **Reranking**: Add relevance reranking
5. **Caching**: Implement embedding caching
