package com.multimodal.ragagent.repository;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface NoteRepository extends JpaRepository<Note, Long> {
    
    List<Note> findByUser(User user);
    
    Page<Note> findByUser(User user, Pageable pageable);
    
    List<Note> findByUserAndNoteType(User user, Note.NoteType noteType);
    
    List<Note> findByUserAndStatus(User user, Note.NoteStatus status);
    
    List<Note> findByDocument(Document document);
    
    Optional<Note> findByIdAndUser(Long id, User user);
    
    @Query("SELECT n FROM Note n WHERE n.user = :user AND n.title LIKE %:title%")
    List<Note> findByUserAndTitleContaining(@Param("user") User user, @Param("title") String title);
    
    @Query("SELECT n FROM Note n WHERE n.user = :user AND (n.title LIKE %:searchTerm% OR n.content LIKE %:searchTerm%)")
    List<Note> findByUserAndTitleOrContentContaining(@Param("user") User user, @Param("searchTerm") String searchTerm);
    
    long countByUser(User user);
    
    long countByUserAndNoteType(User user, Note.NoteType noteType);
    
    long countByUserAndStatus(User user, Note.NoteStatus status);
}
