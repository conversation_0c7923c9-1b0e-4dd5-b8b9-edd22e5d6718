Write-Host "Testing Hugging Face Embedding Service..."

$headers = @{
    'Content-Type' = 'application/json'
}

$body = @{
    text = "This is a test sentence for embedding generation."
} | ConvertTo-Json

try {
    Write-Host "Sending request to test embedding endpoint..."
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/test/embedding" -Method POST -Headers $headers -Body $body
    
    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "Response:" -ForegroundColor Cyan
    $response | ConvertTo-Json -Depth 3
    
} catch {
    Write-Host "ERROR:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    
    if ($_.Exception.Response) {
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        Write-Host "Status Description:" $_.Exception.Response.StatusDescription
    }
}
