package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import com.multimodal.ragagent.repository.ChatSessionRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.cache.annotation.CacheEvict;
import org.springframework.cache.annotation.CachePut;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.time.LocalDateTime;
import java.util.Arrays;
import java.util.List;
import java.util.UUID;

@Service
public class DocumentService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentService.class);
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private DocumentProcessingService documentProcessingService;

    @Autowired
    private VectorService vectorService;

    @Autowired
    private ChatSessionRepository chatSessionRepository;
    
    @Value("${file.upload.dir}")
    private String uploadDir;
    
    @Value("${file.upload.allowed-types}")
    private String allowedTypes;
    
    private static final long MAX_FILE_SIZE = 50 * 1024 * 1024; // 50MB
    
    public Document uploadDocument(MultipartFile file, User user) throws IOException {
        validateFile(file);
        
        // Create upload directory if it doesn't exist
        Path uploadPath = Paths.get(uploadDir);
        if (!Files.exists(uploadPath)) {
            Files.createDirectories(uploadPath);
        }
        
        // Generate unique filename
        String originalFilename = file.getOriginalFilename();
        String fileExtension = getFileExtension(originalFilename);
        String storedFilename = UUID.randomUUID().toString() + "." + fileExtension;
        Path filePath = uploadPath.resolve(storedFilename);
        
        // Save file to disk
        Files.copy(file.getInputStream(), filePath, StandardCopyOption.REPLACE_EXISTING);
        
        // Determine file type
        Document.FileType fileType = determineFileType(originalFilename, file.getContentType());
        
        // Create document entity
        Document document = new Document(
            originalFilename,
            storedFilename,
            filePath.toString(),
            file.getSize(),
            file.getContentType(),
            fileType,
            user
        );
        
        // Save to database
        Document savedDocument = documentRepository.save(document);
        
        // Start async processing
        processDocumentAsync(savedDocument);
        
        logger.info("Document uploaded successfully: {} by user: {}", originalFilename, user.getUsername());
        
        return savedDocument;
    }
    
    @Cacheable(value = "userDocuments", key = "#user.id + '_' + #pageable.pageNumber + '_' + #pageable.pageSize")
    public Page<Document> getUserDocuments(User user, Pageable pageable) {
        return documentRepository.findByUser(user, pageable);
    }
    
    @Cacheable(value = "documents", key = "#id + '_' + #user.id")
    public Document getDocumentById(Long id, User user) {
        return documentRepository.findByIdAndUser(id, user)
            .orElseThrow(() -> new RuntimeException("Document not found"));
    }
    
    @CacheEvict(value = {"documents", "userDocuments"}, allEntries = true)
    public void deleteDocument(Long id, User user) throws IOException {
        Document document = getDocumentById(id, user);
        
        // Delete from vector database
        try {
            vectorService.deleteDocumentFromIndex(document);
            logger.info("Document removed from vector index: {}", document.getOriginalFilename());
        } catch (Exception e) {
            logger.warn("Failed to remove document from vector index: {} - {}",
                       document.getOriginalFilename(), e.getMessage());
        }

        // Delete associated chat sessions
        try {
            chatSessionRepository.deleteByDocument(document);
            logger.info("Deleted chat sessions for document: {}", document.getOriginalFilename());
        } catch (Exception e) {
            logger.warn("Failed to delete chat sessions for document: {} - {}",
                       document.getOriginalFilename(), e.getMessage());
        }

        // Delete file from disk
        Path filePath = Paths.get(document.getFilePath());
        if (Files.exists(filePath)) {
            Files.delete(filePath);
        }

        // Delete from database
        documentRepository.delete(document);

        logger.info("Document deleted: {} by user: {}", document.getOriginalFilename(), user.getUsername());
    }
    
    public List<Document> searchDocuments(String query, User user) {
        return documentRepository.findByUserAndFilenameContaining(user, query);
    }
    
    public void reprocessDocument(Long id, User user) {
        Document document = getDocumentById(id, user);
        document.setProcessingStatus(Document.ProcessingStatus.PENDING);
        documentRepository.save(document);
        
        processDocumentAsync(document);
    }
    
    @Async
    public void processDocumentAsync(Document document) {
        try {
            document.setProcessingStatus(Document.ProcessingStatus.PROCESSING);
            documentRepository.save(document);
            
            // Process the document
            documentProcessingService.processDocument(document);

            // Index document in vector database
            try {
                vectorService.indexDocument(document);
                logger.info("Document indexed in vector database: {}", document.getOriginalFilename());
            } catch (Exception e) {
                logger.warn("Failed to index document in vector database: {} - {}",
                           document.getOriginalFilename(), e.getMessage());
                // Don't fail the entire processing if vector indexing fails
            }

            document.setProcessingStatus(Document.ProcessingStatus.COMPLETED);
            document.setProcessedAt(LocalDateTime.now());
            documentRepository.save(document);

            logger.info("Document processing completed: {}", document.getOriginalFilename());
            
        } catch (Exception e) {
            logger.error("Document processing failed: {}", document.getOriginalFilename(), e);
            document.setProcessingStatus(Document.ProcessingStatus.FAILED);
            documentRepository.save(document);
        }
    }
    
    private void validateFile(MultipartFile file) {
        if (file.isEmpty()) {
            throw new RuntimeException("File is empty");
        }
        
        if (file.getSize() > MAX_FILE_SIZE) {
            throw new RuntimeException("File size exceeds maximum limit of 50MB");
        }
        
        String filename = file.getOriginalFilename();
        if (filename == null || filename.trim().isEmpty()) {
            throw new RuntimeException("Invalid filename");
        }
        
        String fileExtension = getFileExtension(filename).toLowerCase();
        List<String> allowedExtensions = Arrays.asList(allowedTypes.split(","));
        
        if (!allowedExtensions.contains(fileExtension)) {
            throw new RuntimeException("File type not supported. Allowed types: " + allowedTypes);
        }
    }
    
    private String getFileExtension(String filename) {
        if (filename == null || !filename.contains(".")) {
            return "";
        }
        return filename.substring(filename.lastIndexOf(".") + 1);
    }
    
    private Document.FileType determineFileType(String filename, String contentType) {
        String extension = getFileExtension(filename).toLowerCase();

        switch (extension) {
            case "pdf":
                return Document.FileType.PDF;
            case "doc":
                return Document.FileType.DOC;
            case "docx":
                return Document.FileType.DOCX;
            case "ppt":
                return Document.FileType.PPT;
            case "pptx":
                return Document.FileType.PPTX;
            case "csv":
                return Document.FileType.CSV;
            case "txt":
                return Document.FileType.TXT;
            case "xls":
                return Document.FileType.XLS;
            case "xlsx":
                return Document.FileType.XLSX;
            case "rtf":
                return Document.FileType.RTF;
            case "odt":
                return Document.FileType.ODT;
            case "odp":
                return Document.FileType.ODP;
            case "ods":
                return Document.FileType.ODS;
            case "html":
            case "htm":
                return Document.FileType.HTML;
            case "xml":
                return Document.FileType.XML;
            case "json":
                return Document.FileType.JSON;
            case "md":
            case "markdown":
                return Document.FileType.MD;
            default:
                return Document.FileType.UNKNOWN;
        }
    }
}
