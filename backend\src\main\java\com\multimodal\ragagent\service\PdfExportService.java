package com.multimodal.ragagent.service;

import com.itextpdf.kernel.pdf.PdfDocument;
import com.itextpdf.kernel.pdf.PdfWriter;
import com.itextpdf.layout.Document;
import com.itextpdf.layout.element.Paragraph;
import com.itextpdf.layout.element.Text;
import com.itextpdf.layout.properties.TextAlignment;
import com.itextpdf.kernel.font.PdfFont;
import com.itextpdf.kernel.font.PdfFontFactory;
import com.itextpdf.kernel.colors.ColorConstants;

import com.multimodal.ragagent.entity.Note;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.time.format.DateTimeFormatter;

@Service
public class PdfExportService {
    
    private static final Logger logger = LoggerFactory.getLogger(PdfExportService.class);
    
    @Value("${app.export.pdf.enabled:true}")
    private boolean pdfExportEnabled;
    
    public byte[] exportNoteToPdf(Note note) throws IOException {
        if (!pdfExportEnabled) {
            throw new RuntimeException("PDF export is disabled");
        }
        
        try (ByteArrayOutputStream baos = new ByteArrayOutputStream()) {
            PdfWriter writer = new PdfWriter(baos);
            PdfDocument pdfDoc = new PdfDocument(writer);
            Document document = new Document(pdfDoc);
            
            // Set up fonts
            PdfFont titleFont = PdfFontFactory.createFont();
            PdfFont bodyFont = PdfFontFactory.createFont();
            
            // Add title
            Paragraph title = new Paragraph(note.getTitle())
                .setFont(titleFont)
                .setFontSize(18)
                .setBold()
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginBottom(20);
            document.add(title);
            
            // Add metadata
            if (note.getNoteType() != null) {
                Paragraph noteType = new Paragraph("Type: " + note.getNoteType().toString())
                    .setFont(bodyFont)
                    .setFontSize(10)
                    .setFontColor(ColorConstants.GRAY)
                    .setTextAlignment(TextAlignment.CENTER);
                document.add(noteType);
            }
            
            if (note.getCreatedAt() != null) {
                Paragraph createdAt = new Paragraph("Created: " + 
                    note.getCreatedAt().format(DateTimeFormatter.ofPattern("MMM dd, yyyy 'at' HH:mm")))
                    .setFont(bodyFont)
                    .setFontSize(10)
                    .setFontColor(ColorConstants.GRAY)
                    .setTextAlignment(TextAlignment.CENTER)
                    .setMarginBottom(30);
                document.add(createdAt);
            }
            
            // Add content
            String content = note.getContent();
            if (content != null && !content.trim().isEmpty()) {
                // Process markdown-like formatting
                String[] lines = content.split("\n");
                
                for (String line : lines) {
                    if (line.trim().isEmpty()) {
                        document.add(new Paragraph(" ").setMarginBottom(5));
                        continue;
                    }
                    
                    Paragraph paragraph = new Paragraph();
                    
                    // Handle headers
                    if (line.startsWith("# ")) {
                        paragraph.add(new Text(line.substring(2))
                            .setFont(titleFont)
                            .setFontSize(16)
                            .setBold());
                        paragraph.setMarginTop(15).setMarginBottom(10);
                    } else if (line.startsWith("## ")) {
                        paragraph.add(new Text(line.substring(3))
                            .setFont(titleFont)
                            .setFontSize(14)
                            .setBold());
                        paragraph.setMarginTop(12).setMarginBottom(8);
                    } else if (line.startsWith("### ")) {
                        paragraph.add(new Text(line.substring(4))
                            .setFont(titleFont)
                            .setFontSize(12)
                            .setBold());
                        paragraph.setMarginTop(10).setMarginBottom(6);
                    } else if (line.startsWith("- ") || line.startsWith("* ")) {
                        // Bullet points
                        paragraph.add(new Text("• " + line.substring(2))
                            .setFont(bodyFont)
                            .setFontSize(11));
                        paragraph.setMarginLeft(20).setMarginBottom(3);
                    } else if (line.startsWith("**Q:") || line.startsWith("Q:")) {
                        // Questions (for flashcards/Q&A)
                        String questionText = line.startsWith("**Q:") ? line.substring(4).replace("**", "") : line.substring(2);
                        paragraph.add(new Text("Q: " + questionText)
                            .setFont(bodyFont)
                            .setFontSize(11)
                            .setBold()
                            .setFontColor(ColorConstants.BLUE));
                        paragraph.setMarginTop(8).setMarginBottom(4);
                    } else if (line.startsWith("**A:") || line.startsWith("A:")) {
                        // Answers (for flashcards/Q&A)
                        String answerText = line.startsWith("**A:") ? line.substring(4).replace("**", "") : line.substring(2);
                        paragraph.add(new Text("A: " + answerText)
                            .setFont(bodyFont)
                            .setFontSize(11));
                        paragraph.setMarginLeft(15).setMarginBottom(8);
                    } else if (line.startsWith("---")) {
                        // Horizontal line
                        document.add(new Paragraph(" ").setMarginBottom(10));
                        continue;
                    } else {
                        // Regular text
                        paragraph.add(new Text(line)
                            .setFont(bodyFont)
                            .setFontSize(11));
                        paragraph.setMarginBottom(5);
                    }
                    
                    document.add(paragraph);
                }
            }
            
            // Add footer
            Paragraph footer = new Paragraph("Generated by Multimodal RAG Agent")
                .setFont(bodyFont)
                .setFontSize(8)
                .setFontColor(ColorConstants.LIGHT_GRAY)
                .setTextAlignment(TextAlignment.CENTER)
                .setMarginTop(30);
            document.add(footer);
            
            document.close();
            
            logger.info("Successfully exported note '{}' to PDF ({} bytes)", 
                       note.getTitle(), baos.size());
            
            return baos.toByteArray();
            
        } catch (Exception e) {
            logger.error("Failed to export note '{}' to PDF: {}", note.getTitle(), e.getMessage());
            throw new IOException("PDF export failed: " + e.getMessage(), e);
        }
    }
    
    public boolean isPdfExportEnabled() {
        return pdfExportEnabled;
    }
}
