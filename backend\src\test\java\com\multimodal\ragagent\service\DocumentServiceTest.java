package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.mock.web.MockMultipartFile;

import java.util.Arrays;
import java.util.Optional;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DocumentServiceTest {

    @Mock
    private DocumentRepository documentRepository;

    @Mock
    private DocumentProcessingService documentProcessingService;

    @Mock
    private VectorService vectorService;

    @InjectMocks
    private DocumentService documentService;

    private User testUser;
    private Document testDocument;

    @BeforeEach
    void setUp() {
        testUser = new User();
        testUser.setId(1L);
        testUser.setUsername("testuser");
        testUser.setEmail("<EMAIL>");

        testDocument = new Document();
        testDocument.setId(1L);
        testDocument.setOriginalFilename("test.pdf");
        testDocument.setFileSize(1024L);
        testDocument.setContentType("application/pdf");
        testDocument.setFileType(Document.FileType.PDF);
        testDocument.setUser(testUser);
    }

    @Test
    void testGetUserDocuments_Success() {
        // Arrange
        Pageable pageable = PageRequest.of(0, 10);
        Page<Document> expectedPage = new PageImpl<>(Arrays.asList(testDocument));
        when(documentRepository.findByUser(testUser, pageable)).thenReturn(expectedPage);

        // Act
        Page<Document> result = documentService.getUserDocuments(testUser, pageable);

        // Assert
        assertNotNull(result);
        assertEquals(1, result.getContent().size());
        assertEquals(testDocument.getOriginalFilename(), result.getContent().get(0).getOriginalFilename());
        verify(documentRepository).findByUser(testUser, pageable);
    }

    @Test
    void testGetDocumentById_Success() {
        // Arrange
        when(documentRepository.findByIdAndUser(1L, testUser)).thenReturn(Optional.of(testDocument));

        // Act
        Document result = documentService.getDocumentById(1L, testUser);

        // Assert
        assertNotNull(result);
        assertEquals(testDocument.getOriginalFilename(), result.getOriginalFilename());
        verify(documentRepository).findByIdAndUser(1L, testUser);
    }

    @Test
    void testGetDocumentById_NotFound() {
        // Arrange
        when(documentRepository.findByIdAndUser(1L, testUser)).thenReturn(Optional.empty());

        // Act & Assert
        assertThrows(RuntimeException.class, () -> {
            documentService.getDocumentById(1L, testUser);
        });
        verify(documentRepository).findByIdAndUser(1L, testUser);
    }

    @Test
    void testUploadDocument_Success() throws Exception {
        // Arrange
        MockMultipartFile file = new MockMultipartFile(
            "file",
            "test.pdf",
            "application/pdf",
            "test content".getBytes()
        );
        
        when(documentRepository.save(any(Document.class))).thenReturn(testDocument);

        // Act
        Document result = documentService.uploadDocument(file, testUser);

        // Assert
        assertNotNull(result);
        verify(documentRepository).save(any(Document.class));
    }

    @Test
    void testSearchDocuments_Success() {
        // Arrange
        String query = "test";
        when(documentRepository.findByUserAndFilenameContaining(testUser, query))
            .thenReturn(Arrays.asList(testDocument));

        // Act
        var results = documentService.searchDocuments(query, testUser);

        // Assert
        assertNotNull(results);
        assertEquals(1, results.size());
        assertEquals(testDocument.getOriginalFilename(), results.get(0).getOriginalFilename());
        verify(documentRepository).findByUserAndFilenameContaining(testUser, query);
    }
}
