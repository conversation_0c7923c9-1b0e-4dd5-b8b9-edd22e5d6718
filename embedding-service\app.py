#!/usr/bin/env python3
"""
Free Local Embedding Service for Multimodal RAG Agent

This service provides free embeddings using Sentence Transformers.
No API keys or paid services required!

Usage:
    pip install -r requirements.txt
    python app.py

The service will be available at http://localhost:8001
"""

from flask import Flask, request, jsonify
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import logging
import pickle
import os
import os
import sys

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = Flask(__name__)

# Global model variable
model = None
model_name = "sentence-transformers/all-MiniLM-L6-v2"

def load_model():
    """Load the sentence transformer model"""
    global model
    try:
        logger.info(f"Loading model: {model_name}")
        model = SentenceTransformer(model_name)
        logger.info("Model loaded successfully!")
        return True
    except Exception as e:
        logger.error(f"Failed to load model: {e}")
        return False

@app.route('/health', methods=['GET', 'POST'])
def health():
    """Health check endpoint"""
    return jsonify({
        "status": "healthy",
        "model": model_name,
        "model_loaded": model is not None
    })

@app.route('/embed', methods=['POST'])
def embed_single():
    """Generate embedding for a single text"""
    try:
        data = request.get_json()
        
        if not data or 'text' not in data:
            return jsonify({"error": "Missing 'text' field"}), 400
        
        text = data['text']
        
        if not model:
            return jsonify({"error": "Model not loaded"}), 500
        
        # Generate embedding
        embedding = model.encode(text)
        
        # Convert to list for JSON serialization
        embedding_list = embedding.tolist()
        
        return jsonify({
            "embedding": embedding_list,
            "dimensions": len(embedding_list),
            "model": model_name
        })
        
    except Exception as e:
        logger.error(f"Error generating embedding: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/embed_batch', methods=['POST'])
def embed_batch():
    """Generate embeddings for multiple texts"""
    try:
        data = request.get_json()
        
        if not data or 'texts' not in data:
            return jsonify({"error": "Missing 'texts' field"}), 400
        
        texts = data['texts']
        
        if not isinstance(texts, list):
            return jsonify({"error": "'texts' must be a list"}), 400
        
        if not model:
            return jsonify({"error": "Model not loaded"}), 500
        
        # Generate embeddings
        embeddings = model.encode(texts)
        
        # Convert to list for JSON serialization
        embeddings_list = [emb.tolist() for emb in embeddings]
        
        return jsonify({
            "embeddings": embeddings_list,
            "count": len(embeddings_list),
            "dimensions": len(embeddings_list[0]) if embeddings_list else 0,
            "model": model_name
        })
        
    except Exception as e:
        logger.error(f"Error generating embeddings: {e}")
        return jsonify({"error": str(e)}), 500

@app.route('/models', methods=['GET'])
def list_models():
    """List available models"""
    return jsonify({
        "current_model": model_name,
        "available_models": [
            "sentence-transformers/all-MiniLM-L6-v2",
            "sentence-transformers/all-mpnet-base-v2",
            "sentence-transformers/paraphrase-MiniLM-L6-v2"
        ],
        "note": "To change model, restart the service with MODEL_NAME environment variable"
    })

@app.route('/', methods=['GET'])
def index():
    """Service information"""
    return jsonify({
        "service": "Free Local Embedding Service",
        "version": "1.0.0",
        "model": model_name,
        "model_loaded": model is not None,
        "endpoints": {
            "/health": "Health check",
            "/embed": "Generate single embedding (POST)",
            "/embed_batch": "Generate batch embeddings (POST)",
            "/models": "List available models"
        },
        "usage": {
            "single": {
                "method": "POST",
                "url": "/embed",
                "body": {"text": "Your text here"}
            },
            "batch": {
                "method": "POST", 
                "url": "/embed_batch",
                "body": {"texts": ["Text 1", "Text 2"]}
            }
        }
    })

if __name__ == '__main__':
    # Allow model name to be configured via environment variable
    model_name = os.getenv('MODEL_NAME', model_name)
    
    print("=" * 60)
    print("🚀 FREE LOCAL EMBEDDING SERVICE")
    print("=" * 60)
    print(f"Model: {model_name}")
    print("Port: 8001")
    print("No API keys required!")
    print("=" * 60)
    
    # Load the model
    if not load_model():
        print("❌ Failed to load model. Exiting.")
        sys.exit(1)
    
    print("✅ Model loaded successfully!")
    print("🌐 Starting server at http://localhost:8001")
    print("📖 Visit http://localhost:8001 for API documentation")
    print("=" * 60)
    
    # Start the Flask app
    app.run(
        host='0.0.0.0',
        port=8001,
        debug=False,
        threaded=True
    )
