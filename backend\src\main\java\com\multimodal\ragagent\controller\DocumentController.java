package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.DocumentService;
import com.multimodal.ragagent.service.NotesGenerationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/documents")
@CrossOrigin(origins = "*", maxAge = 3600)
public class DocumentController {
    
    @Autowired
    private DocumentService documentService;

    @Autowired
    private NotesGenerationService notesGenerationService;
    
    @PostMapping("/upload")
    public ResponseEntity<?> uploadDocument(
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal User user) {
        try {
            Document document = documentService.uploadDocument(file, user);

            // Create a simplified document response without user object
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Document uploaded successfully");

            Map<String, Object> documentData = new HashMap<>();
            documentData.put("id", document.getId());
            documentData.put("originalFilename", document.getOriginalFilename());
            documentData.put("fileType", document.getFileType());
            documentData.put("fileSize", document.getFileSize());
            documentData.put("processingStatus", document.getProcessingStatus());
            documentData.put("uploadedAt", document.getCreatedAt());

            response.put("data", documentData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to upload document: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping
    public ResponseEntity<Page<Document>> getAllDocuments(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") 
            ? Sort.by(sortBy).descending() 
            : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Document> documents = documentService.getUserDocuments(user, pageable);
        
        return ResponseEntity.ok(documents);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<?> getDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Document document = documentService.getDocumentById(id, user);
            return ResponseEntity.ok(document);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/{id}/status")
    public ResponseEntity<?> getDocumentStatus(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Document document = documentService.getDocumentById(id, user);
            Map<String, Object> status = new HashMap<>();
            status.put("id", document.getId());
            status.put("status", document.getProcessingStatus());
            status.put("filename", document.getOriginalFilename());
            return ResponseEntity.ok(status);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            documentService.deleteDocument(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Document deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<Document>> searchDocuments(
            @RequestParam String query,
            @AuthenticationPrincipal User user) {
        List<Document> documents = documentService.searchDocuments(query, user);
        return ResponseEntity.ok(documents);
    }
    
    @PostMapping("/{id}/reprocess")
    public ResponseEntity<?> reprocessDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            documentService.reprocessDocument(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Document reprocessing started");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping("/{id}/notes-availability")
    public ResponseEntity<?> checkNotesAvailability(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            boolean canGenerate = notesGenerationService.canGenerateNotes(id, user);

            Map<String, Object> response = new HashMap<>();
            response.put("canGenerate", canGenerate);
            response.put("aiAvailable", notesGenerationService.isAINotesAvailable());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
