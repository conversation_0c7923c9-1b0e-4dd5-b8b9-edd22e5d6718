package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.DocumentService;
import com.multimodal.ragagent.util.SecurityUtils;
import com.multimodal.ragagent.service.NotesGenerationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.media.Content;
import io.swagger.v3.oas.annotations.media.Schema;
import io.swagger.v3.oas.annotations.responses.ApiResponse;
import io.swagger.v3.oas.annotations.responses.ApiResponses;
import io.swagger.v3.oas.annotations.security.SecurityRequirement;
import io.swagger.v3.oas.annotations.tags.Tag;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/documents")
@Tag(name = "Documents", description = "Document management and processing operations")
@SecurityRequirement(name = "Bearer Authentication")
public class DocumentController {
    
    @Autowired
    private DocumentService documentService;

    @Autowired
    private NotesGenerationService notesGenerationService;
    
    @Operation(
        summary = "Upload a document",
        description = "Upload and process a document for RAG operations. Supports PDF, DOC, DOCX, PPT, PPTX, TXT, and CSV files."
    )
    @ApiResponses(value = {
        @ApiResponse(responseCode = "200", description = "Document uploaded successfully"),
        @ApiResponse(responseCode = "400", description = "Invalid file or upload failed"),
        @ApiResponse(responseCode = "429", description = "Rate limit exceeded")
    })
    @PostMapping("/upload")
    public ResponseEntity<?> uploadDocument(
            @Parameter(description = "Document file to upload", required = true)
            @RequestParam("file") MultipartFile file,
            @AuthenticationPrincipal User user) {
        try {
            // Security validations
            if (file.isEmpty()) {
                return ResponseEntity.badRequest().body(Map.of("error", "File is empty"));
            }

            // Validate file size (50MB limit)
            if (!SecurityUtils.isValidFileSize(file.getSize(), 50 * 1024 * 1024)) {
                return ResponseEntity.badRequest().body(Map.of("error", "File size exceeds limit"));
            }

            // Validate filename
            if (!SecurityUtils.isValidFilename(file.getOriginalFilename())) {
                return ResponseEntity.badRequest().body(Map.of("error", "Invalid filename"));
            }

            // Validate content type
            String[] allowedTypes = {"pdf", "doc", "docx", "ppt", "pptx", "txt", "csv"};
            if (!SecurityUtils.isAllowedContentType(file.getContentType(), allowedTypes)) {
                return ResponseEntity.badRequest().body(Map.of("error", "File type not allowed"));
            }

            Document document = documentService.uploadDocument(file, user);

            // Create a simplified document response without user object
            Map<String, Object> response = new HashMap<>();
            response.put("message", "Document uploaded successfully");

            Map<String, Object> documentData = new HashMap<>();
            documentData.put("id", document.getId());
            documentData.put("originalFilename", document.getOriginalFilename());
            documentData.put("fileType", document.getFileType());
            documentData.put("fileSize", document.getFileSize());
            documentData.put("processingStatus", document.getProcessingStatus());
            documentData.put("uploadedAt", document.getCreatedAt());

            response.put("data", documentData);
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to upload document: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping
    public ResponseEntity<Page<Document>> getAllDocuments(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") 
            ? Sort.by(sortBy).descending() 
            : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Document> documents = documentService.getUserDocuments(user, pageable);
        
        return ResponseEntity.ok(documents);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<?> getDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Document document = documentService.getDocumentById(id, user);
            return ResponseEntity.ok(document);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/{id}/status")
    public ResponseEntity<?> getDocumentStatus(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Document document = documentService.getDocumentById(id, user);
            Map<String, Object> status = new HashMap<>();
            status.put("id", document.getId());
            status.put("status", document.getProcessingStatus());
            status.put("filename", document.getOriginalFilename());
            status.put("fileType", document.getFileType().toString());
            status.put("fileSize", document.getFileSize());
            status.put("createdAt", document.getCreatedAt());
            status.put("processedAt", document.getProcessedAt());

            // Add processing statistics if available
            if (document.getMetadata() != null && !document.getMetadata().isEmpty()) {
                try {
                    // Parse metadata JSON if it exists
                    com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
                    Map<String, Object> metadata = mapper.readValue(document.getMetadata(), Map.class);
                    status.put("statistics", metadata);
                } catch (Exception e) {
                    // If metadata parsing fails, just include raw metadata
                    status.put("metadata", document.getMetadata());
                }
            }

            // Add text length if processed
            if (document.getExtractedText() != null) {
                status.put("extractedTextLength", document.getExtractedText().length());

                // Calculate basic statistics on the fly if not in metadata
                if (document.getMetadata() == null || document.getMetadata().isEmpty()) {
                    String text = document.getExtractedText();
                    String[] words = text.trim().split("\\s+");
                    String[] sentences = text.split("[.!?]+");
                    String[] paragraphs = text.split("\n\\s*\n");

                    Map<String, Object> basicStats = new HashMap<>();
                    basicStats.put("wordCount", words.length);
                    basicStats.put("sentenceCount", sentences.length);
                    basicStats.put("paragraphCount", paragraphs.length);
                    basicStats.put("estimatedPages", Math.max(1, words.length / 250));
                    status.put("statistics", basicStats);
                }
            }

            return ResponseEntity.ok(status);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            documentService.deleteDocument(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Document deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<Document>> searchDocuments(
            @RequestParam String query,
            @AuthenticationPrincipal User user) {
        List<Document> documents = documentService.searchDocuments(query, user);
        return ResponseEntity.ok(documents);
    }
    
    @PostMapping("/{id}/reprocess")
    public ResponseEntity<?> reprocessDocument(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            documentService.reprocessDocument(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Document reprocessing started");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }

    @GetMapping("/{id}/notes-availability")
    public ResponseEntity<?> checkNotesAvailability(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            boolean canGenerate = notesGenerationService.canGenerateNotes(id, user);

            Map<String, Object> response = new HashMap<>();
            response.put("canGenerate", canGenerate);
            response.put("aiAvailable", notesGenerationService.isAINotesAvailable());

            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
