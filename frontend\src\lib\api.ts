import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8081';

// Create axios instance
const api = axios.create({
  baseURL: `${API_BASE_URL}/api`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle auth errors
api.interceptors.response.use(
  (response) => response,
  (error) => {
    if (error.response?.status === 401) {
      localStorage.removeItem('token');
      localStorage.removeItem('user');
      window.location.href = '/login';
    }
    return Promise.reject(error);
  }
);

// Auth API
export const authAPI = {
  login: (credentials: { usernameOrEmail: string; password: string }) =>
    api.post('/auth/login', credentials),
  
  register: (userData: {
    username: string;
    email: string;
    password: string;
    firstName?: string;
    lastName?: string;
  }) => api.post('/auth/register', userData),
  
  logout: () => api.post('/auth/logout'),
};

// Documents API
export const documentsAPI = {
  upload: (formData: FormData) =>
    api.post('/documents/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }),
  
  getAll: (page = 0, size = 10) =>
    api.get(`/documents?page=${page}&size=${size}`),
  
  getById: (id: number) => api.get(`/documents/${id}`),
  
  delete: (id: number) => api.delete(`/documents/${id}`),
  
  getProcessingStatus: (id: number) =>
    api.get(`/documents/${id}/status`),
};

// RAG API
export const ragAPI = {
  query: (documentId: number, question: string) =>
    api.post('/rag/query', { documentId, question }),

  chat: (documentId: number, messages: Array<{ role: string; content: string }>) =>
    api.post('/rag/chat', { documentId, messages }),

  getStatus: () => api.get('/rag/status'),
};

// Notes API
export const notesAPI = {
  generate: (documentId: number, noteType: string) =>
    api.post('/notes/generate', { documentId, noteType }),

  getTypes: () => api.get('/notes/types'),

  checkAvailability: (documentId: number) =>
    api.get(`/documents/${documentId}/notes-availability`),

  getStatus: () => api.get('/notes/status'),

  getAll: (page = 0, size = 10) =>
    api.get(`/notes?page=${page}&size=${size}`),

  getById: (id: number) => api.get(`/notes/${id}`),

  update: (id: number, data: { title?: string; content?: string; status?: string }) =>
    api.put(`/notes/${id}`, data),

  delete: (id: number) => api.delete(`/notes/${id}`),

  exportToPdf: (id: number) =>
    api.get(`/notes/${id}/export/pdf`, { responseType: 'blob' }),

  search: (query: string) =>
    api.get(`/notes/search?q=${encodeURIComponent(query)}`),
};

// Chat API
export const chatAPI = {
  createSession: (documentId: number) =>
    api.post('/chat/sessions', { documentId }),

  getSessions: (page = 0, size = 10) =>
    api.get(`/chat/sessions?page=${page}&size=${size}`),

  getSession: (sessionId: number) =>
    api.get(`/chat/sessions/${sessionId}`),

  getMessages: (sessionId: number) =>
    api.get(`/chat/sessions/${sessionId}/messages`),

  sendMessage: (sessionId: number, message: string) =>
    api.post(`/chat/sessions/${sessionId}/messages`, { message }),

  updateTitle: (sessionId: number, title: string) =>
    api.put(`/chat/sessions/${sessionId}/title`, { title }),

  deleteSession: (sessionId: number) =>
    api.delete(`/chat/sessions/${sessionId}`),

  getDocumentSessions: (documentId: number) =>
    api.get(`/chat/documents/${documentId}/sessions`),

  getStatistics: () => api.get('/chat/statistics'),
};

export default api;
