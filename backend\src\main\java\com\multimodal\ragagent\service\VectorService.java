package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import com.multimodal.ragagent.repository.DocumentChunkRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Service
public class VectorService {
    
    private static final Logger logger = LoggerFactory.getLogger(VectorService.class);
    
    @Autowired
    private ChromaDBService chromaDBService;
    
    @Autowired
    private EmbeddingService embeddingService;
    
    @Autowired
    private DocumentChunkRepository documentChunkRepository;
    
    public void initializeVectorDatabase() {
        try {
            if (!chromaDBService.isHealthy()) {
                logger.warn("ChromaDB is not healthy. Please ensure ChromaDB is running on the configured host and port.");
                return;
            }
            
            chromaDBService.createCollection();
            logger.info("Vector database initialized successfully");
        } catch (Exception e) {
            logger.error("Failed to initialize vector database: {}", e.getMessage());
        }
    }
    
    public void indexDocument(Document document) {
        try {
            if (!embeddingService.isConfigured()) {
                logger.warn("Embedding service not configured. Skipping vector indexing for document: {}", 
                           document.getOriginalFilename());
                return;
            }
            
            if (!chromaDBService.isHealthy()) {
                logger.warn("ChromaDB not available. Skipping vector indexing for document: {}", 
                           document.getOriginalFilename());
                return;
            }
            
            List<DocumentChunk> chunks = documentChunkRepository.findByDocumentOrderByChunkIndex(document);
            
            if (chunks.isEmpty()) {
                logger.warn("No chunks found for document: {}", document.getOriginalFilename());
                return;
            }
            
            logger.info("Indexing {} chunks for document: {}", chunks.size(), document.getOriginalFilename());
            
            // Prepare data for ChromaDB
            List<String> ids = new ArrayList<>();
            List<String> texts = new ArrayList<>();
            List<Map<String, Object>> metadatas = new ArrayList<>();
            
            for (DocumentChunk chunk : chunks) {
                String chunkId = generateChunkId(document.getId(), chunk.getChunkIndex());
                ids.add(chunkId);
                texts.add(chunk.getContent());
                
                Map<String, Object> metadata = new HashMap<>();
                metadata.put("document_id", document.getId());
                metadata.put("chunk_index", chunk.getChunkIndex());
                metadata.put("start_position", chunk.getStartPosition());
                metadata.put("end_position", chunk.getEndPosition());
                metadata.put("document_filename", document.getOriginalFilename());
                metadata.put("document_type", document.getFileType().toString());
                metadatas.add(metadata);
            }
            
            // Generate embeddings
            List<List<Double>> embeddings = embeddingService.generateEmbeddings(texts);
            
            // Store in ChromaDB
            chromaDBService.addEmbeddings(ids, embeddings, texts, metadatas);
            
            logger.info("Successfully indexed document: {} with {} chunks", 
                       document.getOriginalFilename(), chunks.size());
            
        } catch (Exception e) {
            logger.error("Failed to index document {}: {}", document.getOriginalFilename(), e.getMessage());
            throw new RuntimeException("Failed to index document", e);
        }
    }
    
    public List<DocumentChunk> searchSimilarChunks(String query, int maxResults) {
        try {
            if (!embeddingService.isConfigured()) {
                logger.warn("Embedding service not configured. Cannot perform similarity search.");
                return new ArrayList<>();
            }
            
            if (!chromaDBService.isHealthy()) {
                logger.warn("ChromaDB not available. Cannot perform similarity search.");
                return new ArrayList<>();
            }
            
            // Generate embedding for the query
            List<Double> queryEmbedding = embeddingService.generateEmbedding(query);
            
            // Search in ChromaDB
            QueryResult result = chromaDBService.queryEmbeddings(queryEmbedding, maxResults);
            
            // Convert results to DocumentChunk objects
            List<DocumentChunk> similarChunks = new ArrayList<>();
            
            for (QueryResult.ResultItem item : result.getResults()) {
                if (item.getDocumentId() != null && item.getChunkIndex() != null) {
                    Optional<DocumentChunk> chunkOpt = documentChunkRepository
                            .findByDocumentIdAndChunkIndex(item.getDocumentId(), item.getChunkIndex());
                    
                    if (chunkOpt.isPresent()) {
                        DocumentChunk chunk = chunkOpt.get();
                        // Store similarity score in metadata for later use
                        chunk.setMetadata(String.format("{\"similarity_score\": %f}", 1.0 - item.getDistance()));
                        similarChunks.add(chunk);
                    }
                }
            }
            
            logger.info("Found {} similar chunks for query: {}", similarChunks.size(), 
                       query.length() > 50 ? query.substring(0, 50) + "..." : query);
            
            return similarChunks;
            
        } catch (Exception e) {
            logger.error("Failed to search similar chunks: {}", e.getMessage());
            return new ArrayList<>();
        }
    }
    
    public void deleteDocumentFromIndex(Document document) {
        try {
            if (!chromaDBService.isHealthy()) {
                logger.warn("ChromaDB not available. Cannot delete document from index.");
                return;
            }
            
            List<DocumentChunk> chunks = documentChunkRepository.findByDocumentOrderByChunkIndex(document);
            
            List<String> idsToDelete = chunks.stream()
                    .map(chunk -> generateChunkId(document.getId(), chunk.getChunkIndex()))
                    .collect(Collectors.toList());
            
            if (!idsToDelete.isEmpty()) {
                chromaDBService.deleteEmbeddings(idsToDelete);
                logger.info("Deleted {} chunks from vector index for document: {}", 
                           idsToDelete.size(), document.getOriginalFilename());
            }
            
        } catch (Exception e) {
            logger.error("Failed to delete document from index: {}", e.getMessage());
        }
    }
    
    public boolean isVectorDatabaseAvailable() {
        return chromaDBService.isHealthy() && embeddingService.isConfigured();
    }
    
    public String getVectorDatabaseStatus() {
        boolean chromaHealthy = chromaDBService.isHealthy();
        boolean embeddingConfigured = embeddingService.isConfigured();
        
        if (chromaHealthy && embeddingConfigured) {
            return "Available";
        } else if (!chromaHealthy && !embeddingConfigured) {
            return "ChromaDB and OpenAI not available";
        } else if (!chromaHealthy) {
            return "ChromaDB not available";
        } else {
            return "OpenAI API not configured";
        }
    }
    
    private String generateChunkId(Long documentId, Integer chunkIndex) {
        return String.format("doc_%d_chunk_%d", documentId, chunkIndex);
    }
    
    public void reindexDocument(Document document) {
        // First delete existing embeddings
        deleteDocumentFromIndex(document);
        
        // Then index again
        indexDocument(document);
    }
}
