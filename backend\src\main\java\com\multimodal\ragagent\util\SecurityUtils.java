package com.multimodal.ragagent.util;

import org.springframework.web.util.HtmlUtils;
import java.util.regex.Pattern;

public class SecurityUtils {
    
    // Patterns for validation
    private static final Pattern FILENAME_PATTERN = Pattern.compile("^[a-zA-Z0-9._-]+$");
    private static final Pattern SAFE_TEXT_PATTERN = Pattern.compile("^[a-zA-Z0-9\\s.,!?;:()\\[\\]{}\"'-]+$");
    private static final Pattern SQL_INJECTION_PATTERN = Pattern.compile(
        "(?i)(union|select|insert|update|delete|drop|create|alter|exec|execute|script|javascript|vbscript|onload|onerror)"
    );
    
    /**
     * Sanitize HTML content to prevent XSS attacks
     */
    public static String sanitizeHtml(String input) {
        if (input == null) return null;
        return HtmlUtils.htmlEscape(input);
    }
    
    /**
     * Validate filename to prevent path traversal attacks
     */
    public static boolean isValidFilename(String filename) {
        if (filename == null || filename.trim().isEmpty()) {
            return false;
        }
        
        // Check for path traversal attempts
        if (filename.contains("..") || filename.contains("/") || filename.contains("\\")) {
            return false;
        }
        
        // Check against allowed pattern
        return FILENAME_PATTERN.matcher(filename).matches();
    }
    
    /**
     * Validate text input for safety
     */
    public static boolean isSafeText(String text) {
        if (text == null) return true;
        
        // Check for potential SQL injection
        if (SQL_INJECTION_PATTERN.matcher(text).find()) {
            return false;
        }
        
        return true;
    }
    
    /**
     * Sanitize search query
     */
    public static String sanitizeSearchQuery(String query) {
        if (query == null) return null;
        
        // Remove potentially dangerous characters
        return query.replaceAll("[<>\"'%;()&+]", "")
                   .trim()
                   .substring(0, Math.min(query.length(), 100)); // Limit length
    }
    
    /**
     * Validate file size
     */
    public static boolean isValidFileSize(long fileSize, long maxSize) {
        return fileSize > 0 && fileSize <= maxSize;
    }
    
    /**
     * Validate content type
     */
    public static boolean isAllowedContentType(String contentType, String[] allowedTypes) {
        if (contentType == null || allowedTypes == null) {
            return false;
        }
        
        for (String allowedType : allowedTypes) {
            if (contentType.toLowerCase().contains(allowedType.toLowerCase())) {
                return true;
            }
        }
        return false;
    }
    
    /**
     * Generate safe error message (avoid information disclosure)
     */
    public static String getSafeErrorMessage(String internalMessage) {
        // Don't expose internal details in production
        String profile = System.getProperty("spring.profiles.active", "");
        if (profile.contains("prod")) {
            return "An error occurred. Please try again later.";
        }
        return internalMessage;
    }
}
