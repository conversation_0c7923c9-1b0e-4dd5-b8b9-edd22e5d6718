package com.multimodal.ragagent.repository;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface DocumentChunkRepository extends JpaRepository<DocumentChunk, Long> {
    
    List<DocumentChunk> findByDocument(Document document);
    
    List<DocumentChunk> findByDocumentOrderByChunkIndex(Document document);
    
    List<DocumentChunk> findByDocumentAndChunkType(Document document, DocumentChunk.ChunkType chunkType);
    
    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.document = :document AND dc.content LIKE %:searchTerm%")
    List<DocumentChunk> findByDocumentAndContentContaining(@Param("document") Document document, 
                                                          @Param("searchTerm") String searchTerm);
    
    long countByDocument(Document document);
    
    void deleteByDocument(Document document);

    @Query("SELECT dc FROM DocumentChunk dc WHERE dc.document.id = :documentId AND dc.chunkIndex = :chunkIndex")
    Optional<DocumentChunk> findByDocumentIdAndChunkIndex(@Param("documentId") Long documentId,
                                                         @Param("chunkIndex") Integer chunkIndex);
}
