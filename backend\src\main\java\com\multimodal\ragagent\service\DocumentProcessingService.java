package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import com.multimodal.ragagent.repository.DocumentChunkRepository;
import com.multimodal.ragagent.repository.DocumentRepository;
// Document processing imports - will be implemented later
// import org.apache.pdfbox.pdmodel.PDDocument;
// import org.apache.pdfbox.text.PDFTextStripper;
// import org.apache.poi.hwpf.HWPFDocument;
// import org.apache.poi.hwpf.extractor.WordExtractor;
// import org.apache.poi.xwpf.usermodel.XWPFDocument;
// import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
// import org.apache.poi.hslf.usermodel.HSLFSlideShow;
// import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Service
public class DocumentProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessingService.class);
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private DocumentChunkRepository documentChunkRepository;

    @Autowired
    private EnhancedDocumentProcessor enhancedProcessor;
    
    @Autowired
    private TextChunkingService textChunkingService;
    
    @Value("${rag.chunk-size:1000}")
    private int chunkSize;
    
    @Value("${rag.chunk-overlap:200}")
    private int chunkOverlap;
    
    public void processDocument(Document document) throws IOException {
        try {
            // Try enhanced processing first
            processDocumentEnhanced(document);
        } catch (Exception e) {
            logger.warn("Enhanced processing failed, falling back to basic processing: {}", e.getMessage());
            processDocumentBasic(document);
        }
    }

    private void processDocumentEnhanced(Document document) {
        logger.info("Starting enhanced processing for document: {}", document.getOriginalFilename());

        try {
            // Use enhanced processor
            EnhancedDocumentProcessor.ProcessingResult result = enhancedProcessor.processDocument(document);

            // Update document with extracted text
            document.setExtractedText(result.getExtractedText());
            documentRepository.save(document);

            // Convert enhanced chunks to DocumentChunk entities
            List<DocumentChunk> chunks = convertToDocumentChunks(document, result.getTextChunks());

            // Save chunks
            documentChunkRepository.saveAll(chunks);

            logger.info("Enhanced processing completed for document: {} - {} characters, {} chunks, {} images",
                       document.getOriginalFilename(),
                       result.getCharacterCount(),
                       chunks.size(),
                       result.getExtractedImages().size());

        } catch (Exception e) {
            logger.error("Enhanced processing failed for document {}: {}", document.getOriginalFilename(), e.getMessage());
            throw e;
        }
    }

    private void processDocumentBasic(Document document) throws IOException {
        logger.info("Starting basic processing for document: {}", document.getOriginalFilename());

        String extractedText = extractTextFromDocument(document);

        if (extractedText != null && !extractedText.trim().isEmpty()) {
            // Save extracted text to document
            document.setExtractedText(extractedText);
            documentRepository.save(document);

            // Create text chunks
            List<String> chunks = textChunkingService.chunkText(extractedText, chunkSize, chunkOverlap);
            saveDocumentChunks(document, chunks);

            logger.info("Basic processing completed. Extracted {} characters, created {} chunks",
                       extractedText.length(), chunks.size());
        } else {
            logger.warn("No text extracted from document: {}", document.getOriginalFilename());
        }
    }
    
    private String extractTextFromDocument(Document document) throws IOException {
        File file = new File(document.getFilePath());
        
        switch (document.getFileType()) {
            case PDF:
                return extractTextFromPDF(file);
            case DOC:
                return extractTextFromDOC(file);
            case DOCX:
                return extractTextFromDOCX(file);
            case PPT:
                return extractTextFromPPT(file);
            case PPTX:
                return extractTextFromPPTX(file);
            case CSV:
                return extractTextFromCSV(file);
            case TXT:
                return extractTextFromTXT(file);
            default:
                logger.warn("Unsupported file type: {}", document.getFileType());
                return null;
        }
    }
    
    private String extractTextFromPDF(File file) throws IOException {
        // For now, return a placeholder - we'll implement proper PDF processing later
        return "PDF processing not yet implemented. File: " + file.getName();
    }
    
    private String extractTextFromDOC(File file) throws IOException {
        return "DOC processing not yet implemented. File: " + file.getName();
    }

    private String extractTextFromDOCX(File file) throws IOException {
        return "DOCX processing not yet implemented. File: " + file.getName();
    }

    private String extractTextFromPPT(File file) throws IOException {
        return "PPT processing not yet implemented. File: " + file.getName();
    }

    private String extractTextFromPPTX(File file) throws IOException {
        return "PPTX processing not yet implemented. File: " + file.getName();
    }
    
    private String extractTextFromCSV(File file) throws IOException {
        return Files.readString(Paths.get(file.getPath()));
    }
    
    private String extractTextFromTXT(File file) throws IOException {
        return Files.readString(Paths.get(file.getPath()));
    }
    
    private void saveDocumentChunks(Document document, List<String> chunks) {
        List<DocumentChunk> documentChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            String chunkText = chunks.get(i);
            DocumentChunk chunk = new DocumentChunk(
                chunkText,
                i,
                i * (chunkSize - chunkOverlap),
                i * (chunkSize - chunkOverlap) + chunkText.length(),
                document
            );
            documentChunks.add(chunk);
        }
        
        documentChunkRepository.saveAll(documentChunks);
        logger.info("Saved {} chunks for document: {}", documentChunks.size(), document.getOriginalFilename());
    }

    private List<DocumentChunk> convertToDocumentChunks(Document document, List<EnhancedDocumentProcessor.TextChunk> textChunks) {
        List<DocumentChunk> documentChunks = new ArrayList<>();

        for (EnhancedDocumentProcessor.TextChunk textChunk : textChunks) {
            DocumentChunk documentChunk = new DocumentChunk();
            documentChunk.setDocument(document);
            documentChunk.setContent(textChunk.getContent());
            documentChunk.setChunkIndex(textChunk.getChunkIndex());
            documentChunk.setStartPosition(textChunk.getStartPosition());
            documentChunk.setEndPosition(textChunk.getEndPosition());
            documentChunk.setMetadata(String.format("{\"chunkType\": \"%s\"}", textChunk.getChunkType()));

            documentChunks.add(documentChunk);
        }

        return documentChunks;
    }
}
