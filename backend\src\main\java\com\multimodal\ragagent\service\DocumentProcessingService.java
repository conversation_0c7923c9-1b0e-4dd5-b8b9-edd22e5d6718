package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import com.multimodal.ragagent.repository.DocumentChunkRepository;
import com.multimodal.ragagent.repository.DocumentRepository;
// Document processing imports
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.poi.hwpf.HWPFDocument;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.extractor.XWPFWordExtractor;
import org.apache.poi.hslf.usermodel.HSLFSlideShow;
import org.apache.poi.xslf.usermodel.XMLSlideShow;
import org.apache.poi.sl.extractor.SlideShowExtractor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

@Service
public class DocumentProcessingService {
    
    private static final Logger logger = LoggerFactory.getLogger(DocumentProcessingService.class);
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private DocumentChunkRepository documentChunkRepository;

    @Autowired
    private EnhancedDocumentProcessor enhancedProcessor;
    
    @Autowired
    private TextChunkingService textChunkingService;
    
    @Value("${rag.chunk-size:1000}")
    private int chunkSize;
    
    @Value("${rag.chunk-overlap:200}")
    private int chunkOverlap;
    
    public void processDocument(Document document) throws IOException {
        logger.info("Starting document processing for: {}", document.getOriginalFilename());

        try {
            // Update status to processing
            document.setProcessingStatus(Document.ProcessingStatus.PROCESSING);
            documentRepository.save(document);

            // Try enhanced processing first
            processDocumentEnhanced(document);

            // Mark as completed
            document.setProcessingStatus(Document.ProcessingStatus.COMPLETED);
            document.setProcessedAt(java.time.LocalDateTime.now());
            documentRepository.save(document);

            logger.info("Document processing completed successfully for: {}", document.getOriginalFilename());

        } catch (Exception e) {
            logger.warn("Enhanced processing failed, falling back to basic processing: {}", e.getMessage());

            try {
                processDocumentBasic(document);

                // Mark as completed
                document.setProcessingStatus(Document.ProcessingStatus.COMPLETED);
                document.setProcessedAt(java.time.LocalDateTime.now());
                documentRepository.save(document);

                logger.info("Document processing completed with basic processing for: {}", document.getOriginalFilename());

            } catch (Exception basicError) {
                // Mark as failed
                document.setProcessingStatus(Document.ProcessingStatus.FAILED);
                documentRepository.save(document);

                logger.error("Both enhanced and basic processing failed for: {}", document.getOriginalFilename());
                throw new IOException("Document processing failed", basicError);
            }
        }
    }

    private void processDocumentEnhanced(Document document) {
        logger.info("Starting enhanced processing for document: {}", document.getOriginalFilename());

        try {
            // Use enhanced processor
            EnhancedDocumentProcessor.ProcessingResult result = enhancedProcessor.processDocument(document);

            // Update document with extracted text
            document.setExtractedText(result.getExtractedText());
            documentRepository.save(document);

            // Convert enhanced chunks to DocumentChunk entities
            List<DocumentChunk> chunks = convertToDocumentChunks(document, result.getTextChunks());

            // Save chunks
            documentChunkRepository.saveAll(chunks);

            logger.info("Enhanced processing completed for document: {} - {} characters, {} chunks, {} images",
                       document.getOriginalFilename(),
                       result.getCharacterCount(),
                       chunks.size(),
                       result.getExtractedImages().size());

        } catch (Exception e) {
            logger.error("Enhanced processing failed for document {}: {}", document.getOriginalFilename(), e.getMessage());
            throw e;
        }
    }

    private void processDocumentBasic(Document document) throws IOException {
        logger.info("Starting basic processing for document: {}", document.getOriginalFilename());

        String extractedText = extractTextFromDocument(document);

        if (extractedText != null && !extractedText.trim().isEmpty()) {
            // Save extracted text to document
            document.setExtractedText(extractedText);
            documentRepository.save(document);

            // Create text chunks
            List<String> chunks = textChunkingService.chunkText(extractedText, chunkSize, chunkOverlap);
            saveDocumentChunks(document, chunks);

            logger.info("Basic processing completed. Extracted {} characters, created {} chunks",
                       extractedText.length(), chunks.size());
        } else {
            logger.warn("No text extracted from document: {}", document.getOriginalFilename());
        }
    }
    
    private String extractTextFromDocument(Document document) throws IOException {
        File file = new File(document.getFilePath());
        
        switch (document.getFileType()) {
            case PDF:
                return extractTextFromPDF(file);
            case DOC:
                return extractTextFromDOC(file);
            case DOCX:
                return extractTextFromDOCX(file);
            case PPT:
                return extractTextFromPPT(file);
            case PPTX:
                return extractTextFromPPTX(file);
            case CSV:
                return extractTextFromCSV(file);
            case TXT:
                return extractTextFromTXT(file);
            case XLS:
            case XLSX:
                return extractTextFromExcel(file);
            case RTF:
            case ODT:
            case ODP:
            case ODS:
            case HTML:
            case XML:
            case JSON:
            case MD:
                return extractTextWithTika(file);
            default:
                logger.warn("Unsupported file type: {}, attempting Tika extraction", document.getFileType());
                return extractTextWithTika(file);
        }
    }
    
    private String extractTextFromPDF(File file) throws IOException {
        try (PDDocument document = PDDocument.load(file)) {
            PDFTextStripper stripper = new PDFTextStripper();
            String text = stripper.getText(document);

            // Also extract table data if present
            String tableText = extractTablesFromPDF(document);
            if (!tableText.isEmpty()) {
                text += "\n\n=== EXTRACTED TABLES ===\n" + tableText;
            }

            logger.debug("Extracted {} characters from PDF: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text from PDF {}: {}", file.getName(), e.getMessage());
            throw new IOException("PDF text extraction failed", e);
        }
    }

    private String extractTablesFromPDF(PDDocument document) {
        StringBuilder tableText = new StringBuilder();
        try {
            PDFTextStripper stripper = new PDFTextStripper();
            String fullText = stripper.getText(document);

            // Enhanced table detection with multiple strategies
            String[] lines = fullText.split("\n");
            List<String> potentialTableLines = new ArrayList<>();

            for (String line : lines) {
                if (line.trim().length() > 0) {
                    // Strategy 1: Lines with multiple tabs or spaces (traditional table format)
                    if (line.contains("\t") || line.matches(".*\\s{3,}.*")) {
                        potentialTableLines.add(line);
                    }
                    // Strategy 2: Lines with pipe separators (markdown-style tables)
                    else if (line.contains("|") && line.split("\\|").length > 2) {
                        potentialTableLines.add(line);
                    }
                    // Strategy 3: Lines with consistent comma separation (CSV-like)
                    else if (line.split(",").length > 3 && !line.matches(".*[a-zA-Z]{20,}.*")) {
                        potentialTableLines.add(line);
                    }
                }
            }

            // Group consecutive table lines
            if (!potentialTableLines.isEmpty()) {
                tableText.append("=== DETECTED TABLES ===\n");

                List<String> currentTable = new ArrayList<>();
                for (int i = 0; i < potentialTableLines.size(); i++) {
                    currentTable.add(potentialTableLines.get(i));

                    // Check if next line continues the table pattern
                    boolean isLastLine = (i == potentialTableLines.size() - 1);
                    boolean nextLineBreaksPattern = false;

                    if (!isLastLine) {
                        String currentLine = potentialTableLines.get(i);
                        String nextLine = potentialTableLines.get(i + 1);

                        // Simple heuristic: if column count differs significantly, it's a new table
                        int currentCols = Math.max(currentLine.split("\\s{2,}").length,
                                                 Math.max(currentLine.split("\\|").length,
                                                         currentLine.split(",").length));
                        int nextCols = Math.max(nextLine.split("\\s{2,}").length,
                                              Math.max(nextLine.split("\\|").length,
                                                      nextLine.split(",").length));

                        nextLineBreaksPattern = Math.abs(currentCols - nextCols) > 2;
                    }

                    if (isLastLine || nextLineBreaksPattern) {
                        // Output current table
                        if (currentTable.size() >= 2) { // At least header + 1 row
                            tableText.append("\n--- Table ").append(tableText.toString().split("--- Table").length)
                                    .append(" ---\n");
                            for (String tableLine : currentTable) {
                                tableText.append(tableLine).append("\n");
                            }
                            tableText.append("\n");
                        }
                        currentTable.clear();
                    }
                }
            }

        } catch (Exception e) {
            logger.warn("Failed to extract tables from PDF: {}", e.getMessage());
        }
        return tableText.toString();
    }

    private String extractTextFromDOC(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             HWPFDocument document = new HWPFDocument(fis);
             WordExtractor extractor = new WordExtractor(document)) {

            String text = extractor.getText();
            logger.debug("Extracted {} characters from DOC: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text from DOC {}: {}", file.getName(), e.getMessage());
            throw new IOException("DOC text extraction failed", e);
        }
    }

    private String extractTextFromDOCX(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             XWPFDocument document = new XWPFDocument(fis);
             XWPFWordExtractor extractor = new XWPFWordExtractor(document)) {

            String text = extractor.getText();
            logger.debug("Extracted {} characters from DOCX: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text from DOCX {}: {}", file.getName(), e.getMessage());
            throw new IOException("DOCX text extraction failed", e);
        }
    }

    private String extractTextFromPPT(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             HSLFSlideShow slideShow = new HSLFSlideShow(fis);
             SlideShowExtractor<?,?> extractor = new SlideShowExtractor<>(slideShow)) {

            String text = extractor.getText();
            logger.debug("Extracted {} characters from PPT: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text from PPT {}: {}", file.getName(), e.getMessage());
            throw new IOException("PPT text extraction failed", e);
        }
    }

    private String extractTextFromPPTX(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file);
             XMLSlideShow slideShow = new XMLSlideShow(fis);
             SlideShowExtractor<?,?> extractor = new SlideShowExtractor<>(slideShow)) {

            String text = extractor.getText();
            logger.debug("Extracted {} characters from PPTX: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text from PPTX {}: {}", file.getName(), e.getMessage());
            throw new IOException("PPTX text extraction failed", e);
        }
    }
    
    private String extractTextFromCSV(File file) throws IOException {
        StringBuilder result = new StringBuilder();
        try (FileInputStream fis = new FileInputStream(file)) {
            // Read CSV and format it nicely
            String content = new String(fis.readAllBytes());
            String[] lines = content.split("\n");

            if (lines.length > 0) {
                result.append("=== CSV DATA ===\n");

                // Add header if present
                if (lines.length > 1) {
                    result.append("Headers: ").append(lines[0].trim()).append("\n\n");
                }

                // Add data rows
                for (int i = 1; i < Math.min(lines.length, 1000); i++) { // Limit to first 1000 rows
                    result.append("Row ").append(i).append(": ").append(lines[i].trim()).append("\n");
                }

                if (lines.length > 1000) {
                    result.append("\n... (").append(lines.length - 1000).append(" more rows)\n");
                }
            }

            logger.debug("Processed CSV with {} rows from: {}", lines.length, file.getName());
            return result.toString();
        } catch (Exception e) {
            logger.error("Failed to process CSV {}: {}", file.getName(), e.getMessage());
            // Fallback to simple text reading
            return Files.readString(Paths.get(file.getPath()));
        }
    }
    
    private String extractTextFromTXT(File file) throws IOException {
        return Files.readString(Paths.get(file.getPath()));
    }

    private String extractTextFromExcel(File file) throws IOException {
        try (FileInputStream fis = new FileInputStream(file)) {
            StringBuilder result = new StringBuilder();

            // Use Apache Tika for Excel files as it handles both XLS and XLSX
            org.apache.tika.Tika tika = new org.apache.tika.Tika();
            String text = tika.parseToString(file);

            result.append("=== EXCEL DATA ===\n");
            result.append(text);

            logger.debug("Extracted {} characters from Excel: {}", text.length(), file.getName());
            return result.toString();
        } catch (Exception e) {
            logger.error("Failed to extract text from Excel {}: {}", file.getName(), e.getMessage());
            throw new IOException("Excel text extraction failed", e);
        }
    }

    private String extractTextWithTika(File file) throws IOException {
        try {
            org.apache.tika.Tika tika = new org.apache.tika.Tika();
            String text = tika.parseToString(file);

            logger.debug("Extracted {} characters with Tika from: {}", text.length(), file.getName());
            return text;
        } catch (Exception e) {
            logger.error("Failed to extract text with Tika from {}: {}", file.getName(), e.getMessage());
            throw new IOException("Tika text extraction failed", e);
        }
    }
    
    private void saveDocumentChunks(Document document, List<String> chunks) {
        List<DocumentChunk> documentChunks = new ArrayList<>();
        
        for (int i = 0; i < chunks.size(); i++) {
            String chunkText = chunks.get(i);
            DocumentChunk chunk = new DocumentChunk(
                chunkText,
                i,
                i * (chunkSize - chunkOverlap),
                i * (chunkSize - chunkOverlap) + chunkText.length(),
                document
            );
            documentChunks.add(chunk);
        }
        
        documentChunkRepository.saveAll(documentChunks);
        logger.info("Saved {} chunks for document: {}", documentChunks.size(), document.getOriginalFilename());
    }

    private List<DocumentChunk> convertToDocumentChunks(Document document, List<EnhancedDocumentProcessor.TextChunk> textChunks) {
        List<DocumentChunk> documentChunks = new ArrayList<>();

        for (EnhancedDocumentProcessor.TextChunk textChunk : textChunks) {
            DocumentChunk documentChunk = new DocumentChunk();
            documentChunk.setDocument(document);
            documentChunk.setContent(textChunk.getContent());
            documentChunk.setChunkIndex(textChunk.getChunkIndex());
            documentChunk.setStartPosition(textChunk.getStartPosition());
            documentChunk.setEndPosition(textChunk.getEndPosition());
            documentChunk.setMetadata(String.format("{\"chunkType\": \"%s\"}", textChunk.getChunkType()));

            documentChunks.add(documentChunk);
        }

        return documentChunks;
    }
}
