Write-Host "Checking ChromaDB Collections..."

try {
    $collections = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/collections" -Method GET
    
    if ($collections -and $collections.Count -gt 0) {
        Write-Host "Found $($collections.Count) collection(s):"
        $collections | ConvertTo-Json -Depth 2
    } else {
        Write-Host "No collections found. Upload a document first."
    }
} catch {
    Write-Host "Error: $($_.Exception.Message)"
}
