'use client';

import { useState, useEffect } from 'react';
import Layout from '@/components/Layout';
import RAGQuery from '@/components/RAGQuery';
import ChatInterface from '@/components/ChatInterface';
import MultiDocumentChat from '@/components/MultiDocumentChat';
import SystemStatus from '@/components/SystemStatus';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { 
  Search, 
  Database, 
  FileText, 
  Zap,
  BookOpen,
  MessageSquare
} from 'lucide-react';
import { useDocumentsStore } from '@/store/documentsStore';

export default function RAGPage() {
  const { documents, fetchDocuments } = useDocumentsStore();
  const [selectedDocument, setSelectedDocument] = useState<any>(null);

  useEffect(() => {
    fetchDocuments();
  }, [fetchDocuments]);

  const processedDocuments = documents.filter(
    doc => doc.processingStatus === 'COMPLETED'
  );

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold mb-2">RAG System</h1>
          <p className="text-muted-foreground">
            Query your documents with AI-powered search and get intelligent answers
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6 mb-8">
          {/* Stats Cards */}
          <Card>
            <CardContent className="flex items-center p-6">
              <FileText className="h-8 w-8 text-blue-500 mr-4" />
              <div>
                <p className="text-2xl font-bold">{processedDocuments.length}</p>
                <p className="text-sm text-muted-foreground">Documents Ready</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="flex items-center p-6">
              <Database className="h-8 w-8 text-green-500 mr-4" />
              <div>
                <p className="text-2xl font-bold">Vector DB</p>
                <p className="text-sm text-muted-foreground">Semantic Search</p>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="flex items-center p-6">
              <Zap className="h-8 w-8 text-purple-500 mr-4" />
              <div>
                <p className="text-2xl font-bold">AI Powered</p>
                <p className="text-sm text-muted-foreground">Smart Answers</p>
              </div>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 xl:grid-cols-4 gap-6">
          {/* Document Selector */}
          <Card className="xl:col-span-1">
            <CardHeader>
              <CardTitle className="text-lg">Select Document</CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <div
                className={`p-3 rounded-lg cursor-pointer transition-colors border-2 ${
                  !selectedDocument
                    ? 'border-primary bg-primary/10'
                    : 'border-transparent hover:bg-muted'
                }`}
                onClick={() => setSelectedDocument(null)}
              >
                <div className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  <span className="font-medium">All Documents</span>
                </div>
                <p className="text-xs text-muted-foreground mt-1">
                  Search across all your documents
                </p>
              </div>

              {processedDocuments.length === 0 ? (
                <div className="text-center py-8 text-muted-foreground">
                  <FileText className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No processed documents</p>
                  <p className="text-xs">Upload documents to get started</p>
                </div>
              ) : (
                processedDocuments.map((doc) => (
                  <div
                    key={doc.id}
                    className={`p-3 rounded-lg cursor-pointer transition-colors border-2 ${
                      selectedDocument?.id === doc.id
                        ? 'border-primary bg-primary/10'
                        : 'border-transparent hover:bg-muted'
                    }`}
                    onClick={() => setSelectedDocument(doc)}
                  >
                    <div className="flex items-center gap-2">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium text-sm truncate">
                        {doc.originalFilename}
                      </span>
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="secondary" className="text-xs">
                        {doc.fileType}
                      </Badge>
                      <span className="text-xs text-muted-foreground">
                        {new Date(doc.createdAt).toLocaleDateString()}
                      </span>
                    </div>
                  </div>
                ))
              )}
            </CardContent>
          </Card>

          {/* Main Content */}
          <div className="xl:col-span-3">
            <Tabs defaultValue="query" className="space-y-6">
              <TabsList className="grid w-full grid-cols-4">
                <TabsTrigger value="query" className="flex items-center gap-2">
                  <Search className="h-4 w-4" />
                  Query
                </TabsTrigger>
                <TabsTrigger value="chat" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Chat
                </TabsTrigger>
                <TabsTrigger value="multi-chat" className="flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Multi-Chat
                </TabsTrigger>
                <TabsTrigger value="status" className="flex items-center gap-2">
                  <Database className="h-4 w-4" />
                  Status
                </TabsTrigger>
              </TabsList>

              <TabsContent value="query" className="space-y-6">
                <RAGQuery
                  documentId={selectedDocument?.id}
                  documentName={selectedDocument?.originalFilename}
                />
              </TabsContent>

              <TabsContent value="chat" className="space-y-6">
                {selectedDocument ? (
                  <ChatInterface
                    documentId={selectedDocument.id}
                    documentName={selectedDocument.originalFilename}
                  />
                ) : (
                  <Card>
                    <CardContent className="flex items-center justify-center py-12 text-muted-foreground">
                      <div className="text-center">
                        <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                        <h3 className="font-medium mb-2">Select a Document</h3>
                        <p className="text-sm">
                          Choose a document from the sidebar to start chatting
                        </p>
                      </div>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              <TabsContent value="multi-chat" className="space-y-6">
                <MultiDocumentChat />
              </TabsContent>

              <TabsContent value="status" className="space-y-6">
                <SystemStatus />
                
                {/* Setup Guide */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <BookOpen className="h-5 w-5" />
                      Setup Guide
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div className="space-y-2">
                        <h4 className="font-medium">Free Setup (Recommended)</h4>
                        <div className="text-sm space-y-1">
                          <p>1. Start ChromaDB:</p>
                          <code className="block bg-muted p-2 rounded text-xs">
                            docker run -p 8000:8000 chromadb/chroma
                          </code>
                          <p>2. Start embedding service:</p>
                          <code className="block bg-muted p-2 rounded text-xs">
                            cd embedding-service && python app.py
                          </code>
                        </div>
                      </div>
                      
                      <div className="space-y-2">
                        <h4 className="font-medium">Enhanced Setup (Optional)</h4>
                        <div className="text-sm space-y-1">
                          <p>1. Set Groq API key:</p>
                          <code className="block bg-muted p-2 rounded text-xs">
                            export GROQ_API_KEY=your-key-here
                          </code>
                          <p>2. Restart the application</p>
                          <p className="text-muted-foreground">
                            Enables AI-powered responses and chat
                          </p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </div>
    </Layout>
  );
}
