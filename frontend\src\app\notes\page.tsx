'use client';

import React from 'react';
import Layout from '@/components/Layout';
import NotesGeneration from '@/components/NotesGeneration';
import { useNotesStore } from '@/store/notesStore';
import { toast } from 'sonner';

export default function NotesPage() {
  const { addNote } = useNotesStore();

  const handleNoteGenerated = (note: any) => {
    addNote(note);
    toast.success('Note generated and saved successfully!');
  };

  return (
    <Layout>
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Generate Notes
          </h1>
          <p className="text-gray-600">
            Create AI-powered notes from your uploaded documents using our advanced note generation system.
          </p>
        </div>

        <NotesGeneration onNoteGenerated={handleNoteGenerated} />
      </div>
    </Layout>
  );
}
