package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import com.multimodal.ragagent.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;

@Service
public class NotesService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotesService.class);
    
    @Autowired
    private NoteRepository noteRepository;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    public Page<Note> getUserNotes(User user, Pageable pageable) {
        return noteRepository.findByUser(user, pageable);
    }
    
    public Note getNoteById(Long id, User user) {
        return noteRepository.findByIdAndUser(id, user)
            .orElseThrow(() -> new RuntimeException("Note not found"));
    }
    
    public Note generateNote(Long documentId, Note.NoteType noteType, User user) {
        Document document = documentRepository.findByIdAndUser(documentId, user)
            .orElseThrow(() -> new RuntimeException("Document not found"));
        
        if (document.getProcessingStatus() != Document.ProcessingStatus.COMPLETED) {
            throw new RuntimeException("Document is not yet processed");
        }
        
        // Generate note content based on type
        String title = generateNoteTitle(document, noteType);
        String content = generateNoteContent(document, noteType);
        
        Note note = new Note(title, content, noteType, user, document);
        note.setStatus(Note.NoteStatus.PUBLISHED);
        
        Note savedNote = noteRepository.save(note);
        
        logger.info("Generated {} note for document: {} by user: {}", 
                   noteType, document.getOriginalFilename(), user.getUsername());
        
        return savedNote;
    }
    
    public Note updateNote(Long id, Map<String, Object> updates, User user) {
        Note note = getNoteById(id, user);
        
        if (updates.containsKey("title")) {
            note.setTitle(updates.get("title").toString());
        }
        
        if (updates.containsKey("content")) {
            note.setContent(updates.get("content").toString());
        }
        
        if (updates.containsKey("status")) {
            note.setStatus(Note.NoteStatus.valueOf(updates.get("status").toString()));
        }
        
        return noteRepository.save(note);
    }
    
    public void deleteNote(Long id, User user) {
        Note note = getNoteById(id, user);
        noteRepository.delete(note);
        
        logger.info("Note deleted: {} by user: {}", note.getTitle(), user.getUsername());
    }
    
    public List<Note> searchNotes(String query, User user) {
        return noteRepository.findByUserAndTitleOrContentContaining(user, query);
    }
    
    private String generateNoteTitle(Document document, Note.NoteType noteType) {
        String baseTitle = document.getOriginalFilename().replaceFirst("[.][^.]+$", "");
        
        switch (noteType) {
            case DETAILED:
                return "Detailed Notes: " + baseTitle;
            case QUICK_REVISION:
                return "Quick Revision: " + baseTitle;
            case MEDIUM_LEVEL:
                return "Study Notes: " + baseTitle;
            default:
                return "Notes: " + baseTitle;
        }
    }
    
    private String generateNoteContent(Document document, Note.NoteType noteType) {
        String extractedText = document.getExtractedText();
        
        if (extractedText == null || extractedText.trim().isEmpty()) {
            return "No content available for note generation.";
        }
        
        // For now, return a formatted version of the extracted text
        // Later, this will be replaced with AI-generated content using Groq API
        
        switch (noteType) {
            case DETAILED:
                return generateDetailedNotes(extractedText);
            case QUICK_REVISION:
                return generateQuickRevisionNotes(extractedText);
            case MEDIUM_LEVEL:
                return generateMediumLevelNotes(extractedText);
            default:
                return extractedText;
        }
    }
    
    private String generateDetailedNotes(String text) {
        StringBuilder notes = new StringBuilder();
        notes.append("# Detailed Study Notes\n\n");
        notes.append("## Overview\n");
        notes.append("This document contains comprehensive information that has been processed for detailed study.\n\n");
        notes.append("## Content Summary\n");
        notes.append(text.substring(0, Math.min(text.length(), 500)));
        notes.append("\n\n## Key Points\n");
        notes.append("- Detailed analysis of the content\n");
        notes.append("- Comprehensive explanations\n");
        notes.append("- Real-world applications\n");
        notes.append("\n\n## Full Content\n");
        notes.append(text);
        
        return notes.toString();
    }
    
    private String generateQuickRevisionNotes(String text) {
        StringBuilder notes = new StringBuilder();
        notes.append("# Quick Revision Notes\n\n");
        notes.append("## Key Points for Quick Review\n\n");
        
        // Extract first few sentences as key points
        String[] sentences = text.split("[.!?]+");
        for (int i = 0; i < Math.min(sentences.length, 5); i++) {
            if (sentences[i].trim().length() > 10) {
                notes.append("• ").append(sentences[i].trim()).append("\n");
            }
        }
        
        notes.append("\n## Summary\n");
        notes.append(text.substring(0, Math.min(text.length(), 300)));
        notes.append("...\n");
        
        return notes.toString();
    }
    
    private String generateMediumLevelNotes(String text) {
        StringBuilder notes = new StringBuilder();
        notes.append("# Study Notes\n\n");
        notes.append("## Main Content\n\n");
        
        // Split into paragraphs and format
        String[] paragraphs = text.split("\n\n");
        for (int i = 0; i < Math.min(paragraphs.length, 3); i++) {
            if (paragraphs[i].trim().length() > 20) {
                notes.append("### Section ").append(i + 1).append("\n");
                notes.append(paragraphs[i].trim()).append("\n\n");
            }
        }
        
        notes.append("## Additional Information\n");
        notes.append("For more detailed information, refer to the original document.\n");
        
        return notes.toString();
    }
}
