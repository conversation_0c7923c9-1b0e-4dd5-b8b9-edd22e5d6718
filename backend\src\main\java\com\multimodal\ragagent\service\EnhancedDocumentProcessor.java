package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.apache.pdfbox.rendering.PDFRenderer;
import org.apache.pdfbox.rendering.ImageType;
import org.apache.tika.Tika;
import org.apache.tika.metadata.Metadata;
import org.apache.tika.parser.AutoDetectParser;
import org.apache.tika.parser.ParseContext;
import org.apache.tika.sax.BodyContentHandler;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.awt.image.BufferedImage;
import java.io.*;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class EnhancedDocumentProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(EnhancedDocumentProcessor.class);
    
    @Value("${file.upload.dir}")
    private String uploadDir;
    
    @Value("${document.processing.extract-images:true}")
    private boolean extractImages;
    
    @Value("${document.processing.ocr-enabled:false}")
    private boolean ocrEnabled;
    
    @Value("${document.processing.max-file-size:50MB}")
    private String maxFileSize;
    
    private final Tika tika = new Tika();
    
    public ProcessingResult processDocument(Document document) {
        try {
            logger.info("Starting enhanced processing for document: {}", document.getOriginalFilename());
            
            Path filePath = Paths.get(document.getFilePath());
            if (!Files.exists(filePath)) {
                throw new RuntimeException("Document file not found: " + document.getFilePath());
            }
            
            ProcessingResult result = new ProcessingResult();
            result.setDocument(document);
            
            // Detect document type and metadata
            detectDocumentMetadata(filePath, result);
            
            // Extract text based on document type
            extractText(filePath, result);
            
            // Extract images if enabled
            if (extractImages) {
                extractImages(filePath, result);
            }
            
            // Perform OCR on images if enabled
            if (ocrEnabled && !result.getExtractedImages().isEmpty()) {
                performOCR(result);
            }
            
            // Enhanced text chunking
            performAdvancedChunking(result);
            
            logger.info("Enhanced processing completed for document: {} - extracted {} characters, {} chunks, {} images", 
                       document.getOriginalFilename(), 
                       result.getExtractedText().length(),
                       result.getTextChunks().size(),
                       result.getExtractedImages().size());
            
            return result;
            
        } catch (Exception e) {
            logger.error("Enhanced processing failed for document {}: {}", document.getOriginalFilename(), e.getMessage());
            throw new RuntimeException("Enhanced document processing failed", e);
        }
    }
    
    private void detectDocumentMetadata(Path filePath, ProcessingResult result) throws Exception {
        try (InputStream inputStream = Files.newInputStream(filePath)) {
            Metadata metadata = new Metadata();
            AutoDetectParser parser = new AutoDetectParser();
            BodyContentHandler handler = new BodyContentHandler(-1); // No limit
            ParseContext context = new ParseContext();
            
            parser.parse(inputStream, handler, metadata, context);
            
            Map<String, String> metadataMap = new HashMap<>();
            for (String name : metadata.names()) {
                metadataMap.put(name, metadata.get(name));
            }
            
            result.setMetadata(metadataMap);
            result.setDetectedMimeType(tika.detect(filePath.toFile()));
            
            logger.debug("Detected metadata for {}: {} properties", 
                        filePath.getFileName(), metadataMap.size());
        }
    }
    
    private void extractText(Path filePath, ProcessingResult result) throws Exception {
        String mimeType = result.getDetectedMimeType();
        String extractedText = "";
        
        if (mimeType.equals("application/pdf")) {
            extractedText = extractTextFromPDF(filePath);
        } else {
            // Use Tika for other document types
            extractedText = extractTextWithTika(filePath);
        }
        
        result.setExtractedText(extractedText);
        result.setCharacterCount(extractedText.length());
    }
    
    private String extractTextFromPDF(Path filePath) throws Exception {
        try (PDDocument document = PDDocument.load(filePath.toFile())) {
            PDFTextStripper stripper = new PDFTextStripper();
            
            // Configure stripper for better text extraction
            stripper.setSortByPosition(true);
            stripper.setLineSeparator("\n");
            stripper.setWordSeparator(" ");
            
            String text = stripper.getText(document);
            
            logger.debug("Extracted {} characters from PDF: {}", text.length(), filePath.getFileName());
            return text;
        }
    }
    
    private String extractTextWithTika(Path filePath) throws Exception {
        try (InputStream inputStream = Files.newInputStream(filePath)) {
            BodyContentHandler handler = new BodyContentHandler(-1); // No limit
            Metadata metadata = new Metadata();
            ParseContext context = new ParseContext();
            AutoDetectParser parser = new AutoDetectParser();
            
            parser.parse(inputStream, handler, metadata, context);
            
            String text = handler.toString();
            logger.debug("Extracted {} characters with Tika from: {}", text.length(), filePath.getFileName());
            return text;
        }
    }
    
    private void extractImages(Path filePath, ProcessingResult result) {
        if (!result.getDetectedMimeType().equals("application/pdf")) {
            return; // Only extract images from PDFs for now
        }
        
        try (PDDocument document = PDDocument.load(filePath.toFile())) {
            PDFRenderer renderer = new PDFRenderer(document);
            List<String> imagePaths = new ArrayList<>();
            
            String imageDir = createImageDirectory(result.getDocument());
            
            for (int pageIndex = 0; pageIndex < document.getNumberOfPages(); pageIndex++) {
                BufferedImage image = renderer.renderImageWithDPI(pageIndex, 150, ImageType.RGB);
                
                String imagePath = saveImage(image, imageDir, pageIndex);
                if (imagePath != null) {
                    imagePaths.add(imagePath);
                }
            }
            
            result.setExtractedImages(imagePaths);
            logger.debug("Extracted {} images from PDF: {}", imagePaths.size(), filePath.getFileName());
            
        } catch (Exception e) {
            logger.warn("Failed to extract images from PDF {}: {}", filePath.getFileName(), e.getMessage());
        }
    }
    
    private String createImageDirectory(Document document) throws IOException {
        String imageDir = uploadDir + "/images/" + document.getId();
        Path imageDirPath = Paths.get(imageDir);
        Files.createDirectories(imageDirPath);
        return imageDir;
    }
    
    private String saveImage(BufferedImage image, String imageDir, int pageIndex) {
        try {
            String imagePath = imageDir + "/page_" + pageIndex + ".png";
            javax.imageio.ImageIO.write(image, "PNG", new File(imagePath));
            return imagePath;
        } catch (Exception e) {
            logger.warn("Failed to save image for page {}: {}", pageIndex, e.getMessage());
            return null;
        }
    }
    
    private void performOCR(ProcessingResult result) {
        // OCR implementation would go here
        // For now, we'll skip OCR as it requires Tesseract installation
        logger.info("OCR is configured but not implemented in this version");
    }
    
    private void performAdvancedChunking(ProcessingResult result) {
        String text = result.getExtractedText();
        if (text == null || text.trim().isEmpty()) {
            return;
        }
        
        List<TextChunk> chunks = new ArrayList<>();
        
        // Strategy 1: Paragraph-based chunking
        chunks.addAll(chunkByParagraphs(text));
        
        // Strategy 2: Sentence-based chunking for remaining text
        if (chunks.isEmpty()) {
            chunks.addAll(chunkBySentences(text));
        }
        
        // Strategy 3: Fixed-size chunking as fallback
        if (chunks.isEmpty()) {
            chunks.addAll(chunkByFixedSize(text, 1000, 200));
        }
        
        result.setTextChunks(chunks);
    }
    
    private List<TextChunk> chunkByParagraphs(String text) {
        List<TextChunk> chunks = new ArrayList<>();
        String[] paragraphs = text.split("\n\n+");
        
        int position = 0;
        for (int i = 0; i < paragraphs.length; i++) {
            String paragraph = paragraphs[i].trim();
            if (!paragraph.isEmpty() && paragraph.length() > 50) { // Minimum chunk size
                TextChunk chunk = new TextChunk();
                chunk.setContent(paragraph);
                chunk.setStartPosition(position);
                chunk.setEndPosition(position + paragraph.length());
                chunk.setChunkIndex(i);
                chunk.setChunkType("paragraph");
                chunks.add(chunk);
            }
            position += paragraphs[i].length() + 2; // +2 for \n\n
        }
        
        return chunks;
    }
    
    private List<TextChunk> chunkBySentences(String text) {
        List<TextChunk> chunks = new ArrayList<>();
        String[] sentences = text.split("\\. ");
        
        StringBuilder currentChunk = new StringBuilder();
        int chunkIndex = 0;
        int startPosition = 0;
        
        for (String sentence : sentences) {
            if (currentChunk.length() + sentence.length() > 1000) {
                if (currentChunk.length() > 0) {
                    TextChunk chunk = new TextChunk();
                    chunk.setContent(currentChunk.toString().trim());
                    chunk.setStartPosition(startPosition);
                    chunk.setEndPosition(startPosition + currentChunk.length());
                    chunk.setChunkIndex(chunkIndex++);
                    chunk.setChunkType("sentence");
                    chunks.add(chunk);
                    
                    startPosition += currentChunk.length();
                    currentChunk = new StringBuilder();
                }
            }
            
            currentChunk.append(sentence).append(". ");
        }
        
        // Add the last chunk
        if (currentChunk.length() > 0) {
            TextChunk chunk = new TextChunk();
            chunk.setContent(currentChunk.toString().trim());
            chunk.setStartPosition(startPosition);
            chunk.setEndPosition(startPosition + currentChunk.length());
            chunk.setChunkIndex(chunkIndex);
            chunk.setChunkType("sentence");
            chunks.add(chunk);
        }
        
        return chunks;
    }
    
    private List<TextChunk> chunkByFixedSize(String text, int chunkSize, int overlap) {
        List<TextChunk> chunks = new ArrayList<>();
        int chunkIndex = 0;
        
        for (int i = 0; i < text.length(); i += chunkSize - overlap) {
            int endIndex = Math.min(i + chunkSize, text.length());
            String chunkContent = text.substring(i, endIndex);
            
            TextChunk chunk = new TextChunk();
            chunk.setContent(chunkContent);
            chunk.setStartPosition(i);
            chunk.setEndPosition(endIndex);
            chunk.setChunkIndex(chunkIndex++);
            chunk.setChunkType("fixed");
            chunks.add(chunk);
            
            if (endIndex >= text.length()) {
                break;
            }
        }
        
        return chunks;
    }
    
    // Inner classes for processing results
    public static class ProcessingResult {
        private Document document;
        private String extractedText;
        private List<String> extractedImages = new ArrayList<>();
        private List<TextChunk> textChunks = new ArrayList<>();
        private Map<String, String> metadata = new HashMap<>();
        private String detectedMimeType;
        private int characterCount;
        
        // Getters and setters
        public Document getDocument() { return document; }
        public void setDocument(Document document) { this.document = document; }
        
        public String getExtractedText() { return extractedText; }
        public void setExtractedText(String extractedText) { this.extractedText = extractedText; }
        
        public List<String> getExtractedImages() { return extractedImages; }
        public void setExtractedImages(List<String> extractedImages) { this.extractedImages = extractedImages; }
        
        public List<TextChunk> getTextChunks() { return textChunks; }
        public void setTextChunks(List<TextChunk> textChunks) { this.textChunks = textChunks; }
        
        public Map<String, String> getMetadata() { return metadata; }
        public void setMetadata(Map<String, String> metadata) { this.metadata = metadata; }
        
        public String getDetectedMimeType() { return detectedMimeType; }
        public void setDetectedMimeType(String detectedMimeType) { this.detectedMimeType = detectedMimeType; }
        
        public int getCharacterCount() { return characterCount; }
        public void setCharacterCount(int characterCount) { this.characterCount = characterCount; }
    }
    
    public static class TextChunk {
        private String content;
        private int startPosition;
        private int endPosition;
        private int chunkIndex;
        private String chunkType;
        
        // Getters and setters
        public String getContent() { return content; }
        public void setContent(String content) { this.content = content; }
        
        public int getStartPosition() { return startPosition; }
        public void setStartPosition(int startPosition) { this.startPosition = startPosition; }
        
        public int getEndPosition() { return endPosition; }
        public void setEndPosition(int endPosition) { this.endPosition = endPosition; }
        
        public int getChunkIndex() { return chunkIndex; }
        public void setChunkIndex(int chunkIndex) { this.chunkIndex = chunkIndex; }
        
        public String getChunkType() { return chunkType; }
        public void setChunkType(String chunkType) { this.chunkType = chunkType; }
    }
}
