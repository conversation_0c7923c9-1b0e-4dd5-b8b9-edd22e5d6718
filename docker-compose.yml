version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: multimodal-rag-postgres
    environment:
      POSTGRES_DB: multimodal_rag
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./backend/src/main/resources/db/migration:/docker-entrypoint-initdb.d
    networks:
      - rag-network

  # Chroma Vector Database
  chroma:
    image: chromadb/chroma:latest
    container_name: multimodal-rag-chroma
    ports:
      - "8000:8000"
    volumes:
      - chroma_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - rag-network

  # Spring Boot Backend
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: multimodal-rag-backend
    ports:
      - "8080:8080"
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=postgres
      - DB_PORT=5432
      - DB_NAME=multimodal_rag
      - DB_USERNAME=postgres
      - DB_PASSWORD=password
      - VECTOR_DB_HOST=chroma
      - VECTOR_DB_PORT=8000
      - JWT_SECRET=your-super-secret-jwt-key-change-this-in-production
      - GROQ_API_KEY=${GROQ_API_KEY}
    depends_on:
      - postgres
      - chroma
    volumes:
      - ./uploads:/app/uploads
      - ./backend/logs:/app/logs
    networks:
      - rag-network

  # Next.js Frontend
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: multimodal-rag-frontend
    ports:
      - "3000:3000"
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080
      - NODE_ENV=production
    depends_on:
      - backend
    networks:
      - rag-network

volumes:
  postgres_data:
  chroma_data:

networks:
  rag-network:
    driver: bridge
