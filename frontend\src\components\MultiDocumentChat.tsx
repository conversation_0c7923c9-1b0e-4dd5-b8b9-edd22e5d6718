'use client';

import React, { useState, useEffect, useRef } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { ScrollArea } from '@/components/ui/scroll-area';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Checkbox } from '@/components/ui/checkbox';
import { 
  Loader2, 
  Send, 
  MessageSquare, 
  User, 
  Bot,
  FileText,
  Plus,
  X,
  Search,
  Filter
} from 'lucide-react';
import { chatAPI, documentsAPI } from '@/lib/api';
import { toast } from 'sonner';

interface Document {
  id: number;
  originalFilename: string;
  fileSize: number;
  contentType: string;
  fileType: string;
  processingStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  createdAt: string;
  updatedAt: string;
}

interface ChatMessage {
  id: number;
  role: 'user' | 'assistant';
  content: string;
  createdAt: string;
  responseTimeMs?: number;
  documentContext?: string[];
}

interface ChatSession {
  id: number;
  title: string;
  documentIds: number[];
  documentNames: string[];
  createdAt: string;
  updatedAt: string;
  messageCount: number;
}

export default function MultiDocumentChat() {
  const [documents, setDocuments] = useState<Document[]>([]);
  const [selectedDocuments, setSelectedDocuments] = useState<number[]>([]);
  const [sessions, setSessions] = useState<ChatSession[]>([]);
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [newMessage, setNewMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingDocuments, setLoadingDocuments] = useState(true);
  const [searchQuery, setSearchQuery] = useState('');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    loadDocuments();
    loadSessions();
  }, []);

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  const loadDocuments = async () => {
    try {
      setLoadingDocuments(true);
      const response = await documentsAPI.getAll();
      const docs = response.data.content || response.data || [];
      const completedDocs = docs.filter((doc: Document) => doc.processingStatus === 'COMPLETED');
      setDocuments(completedDocs);
    } catch (error) {
      console.error('Failed to load documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setLoadingDocuments(false);
    }
  };

  const loadSessions = async () => {
    try {
      const response = await chatAPI.getSessions();
      setSessions(response.data.sessions || []);
    } catch (error) {
      console.error('Failed to load chat sessions:', error);
      toast.error('Failed to load chat sessions');
    }
  };

  const loadMessages = async (sessionId: number) => {
    try {
      const response = await chatAPI.getMessages(sessionId);
      setMessages(response.data.messages || []);
    } catch (error) {
      console.error('Failed to load messages:', error);
      toast.error('Failed to load messages');
    }
  };

  const createMultiDocumentSession = async () => {
    if (selectedDocuments.length === 0) {
      toast.error('Please select at least one document');
      return;
    }

    try {
      // For now, create a session with the first document
      // In a full implementation, you'd create a multi-document session
      const response = await chatAPI.createSession(selectedDocuments[0]);
      const newSession = {
        ...response.data.session,
        documentIds: selectedDocuments,
        documentNames: selectedDocuments.map(id => 
          documents.find(doc => doc.id === id)?.originalFilename || 'Unknown'
        )
      };
      
      setSessions([newSession, ...sessions]);
      setCurrentSession(newSession);
      setMessages([]);
      toast.success(`Multi-document chat session created with ${selectedDocuments.length} documents`);
    } catch (error) {
      console.error('Failed to create chat session:', error);
      toast.error('Failed to create chat session');
    }
  };

  const sendMessage = async () => {
    if (!newMessage.trim() || !currentSession) return;

    const messageText = newMessage.trim();
    setNewMessage('');
    setLoading(true);

    // Add user message to UI immediately
    const userMessage: ChatMessage = {
      id: Date.now(),
      role: 'user',
      content: messageText,
      createdAt: new Date().toISOString(),
    };
    setMessages(prev => [...prev, userMessage]);

    try {
      const response = await chatAPI.sendMessage(currentSession.id, messageText);
      const aiMessage = response.data.message;
      
      // Add AI response to messages
      setMessages(prev => [...prev, aiMessage]);
      
      // Update session in list
      setSessions(prev => prev.map(session => 
        session.id === currentSession.id 
          ? { ...session, updatedAt: new Date().toISOString(), messageCount: session.messageCount + 2 }
          : session
      ));
      
    } catch (error) {
      console.error('Failed to send message:', error);
      toast.error('Failed to send message');
      
      // Remove the user message on error
      setMessages(prev => prev.slice(0, -1));
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  const formatTime = (dateString: string) => {
    return new Date(dateString).toLocaleTimeString([], { 
      hour: '2-digit', 
      minute: '2-digit' 
    });
  };

  const filteredDocuments = documents.filter(doc =>
    doc.originalFilename.toLowerCase().includes(searchQuery.toLowerCase()) ||
    doc.fileType.toLowerCase().includes(searchQuery.toLowerCase())
  );

  const toggleDocumentSelection = (documentId: number) => {
    setSelectedDocuments(prev => 
      prev.includes(documentId)
        ? prev.filter(id => id !== documentId)
        : [...prev, documentId]
    );
  };

  if (loadingDocuments) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading documents...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 h-[700px]">
      {/* Document Selection Sidebar */}
      <Card className="lg:col-span-1">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Select Documents</CardTitle>
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search documents..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10 text-sm"
            />
          </div>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[300px]">
            <div className="space-y-2 p-4">
              {filteredDocuments.map((document) => (
                <div
                  key={document.id}
                  className="flex items-center space-x-2 p-2 rounded-lg hover:bg-muted cursor-pointer"
                  onClick={() => toggleDocumentSelection(document.id)}
                >
                  <Checkbox
                    checked={selectedDocuments.includes(document.id)}
                    onChange={() => toggleDocumentSelection(document.id)}
                  />
                  <FileText className="h-4 w-4 text-primary flex-shrink-0" />
                  <div className="flex-1 min-w-0">
                    <div className="text-sm font-medium truncate">
                      {document.originalFilename}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      {document.fileType.toUpperCase()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
          
          <div className="p-4 border-t">
            <div className="text-xs text-muted-foreground mb-2">
              {selectedDocuments.length} document(s) selected
            </div>
            <Button 
              onClick={createMultiDocumentSession}
              disabled={selectedDocuments.length === 0}
              size="sm"
              className="w-full"
            >
              <Plus className="h-4 w-4 mr-2" />
              Start Multi-Doc Chat
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Chat Sessions */}
      <Card className="lg:col-span-1">
        <CardHeader className="pb-3">
          <CardTitle className="text-sm">Chat Sessions</CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ScrollArea className="h-[600px]">
            <div className="space-y-2 p-4">
              {sessions.length === 0 ? (
                <div className="text-center text-muted-foreground py-8">
                  <MessageSquare className="h-8 w-8 mx-auto mb-2 opacity-50" />
                  <p className="text-sm">No chat sessions yet</p>
                </div>
              ) : (
                sessions.map((session) => (
                  <div
                    key={session.id}
                    className={`p-3 rounded-lg cursor-pointer transition-colors ${
                      currentSession?.id === session.id
                        ? 'bg-primary text-primary-foreground'
                        : 'hover:bg-muted'
                    }`}
                    onClick={() => {
                      setCurrentSession(session);
                      loadMessages(session.id);
                    }}
                  >
                    <div className="font-medium text-sm truncate">
                      {session.title}
                    </div>
                    <div className="text-xs opacity-70 mt-1">
                      {session.messageCount} messages
                    </div>
                    {session.documentNames && session.documentNames.length > 1 && (
                      <Badge variant="secondary" className="text-xs mt-1">
                        {session.documentNames.length} docs
                      </Badge>
                    )}
                  </div>
                ))
              )}
            </div>
          </ScrollArea>
        </CardContent>
      </Card>

      {/* Chat Area */}
      <Card className="lg:col-span-2">
        <CardHeader className="pb-3">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            {currentSession ? currentSession.title : 'Multi-Document Chat'}
            {currentSession && (
              <Badge variant="secondary" className="ml-auto">
                {messages.length} messages
              </Badge>
            )}
          </CardTitle>
          {currentSession?.documentNames && currentSession.documentNames.length > 1 && (
            <div className="flex flex-wrap gap-1 mt-2">
              {currentSession.documentNames.map((name, index) => (
                <Badge key={index} variant="outline" className="text-xs">
                  {name}
                </Badge>
              ))}
            </div>
          )}
        </CardHeader>
        <CardContent className="p-0">
          {!currentSession ? (
            <div className="flex items-center justify-center h-[500px] text-muted-foreground">
              <div className="text-center">
                <MessageSquare className="h-12 w-12 mx-auto mb-4 opacity-50" />
                <h3 className="font-medium mb-2">Start a multi-document conversation</h3>
                <p className="text-sm mb-4">Select documents and create a chat session to start</p>
              </div>
            </div>
          ) : (
            <>
              {/* Messages */}
              <ScrollArea className="h-[540px] p-4">
                <div className="space-y-4">
                  {messages.map((message) => (
                    <div
                      key={message.id}
                      className={`flex gap-3 ${
                        message.role === 'user' ? 'justify-end' : 'justify-start'
                      }`}
                    >
                      {message.role === 'assistant' && (
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <Bot className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                      )}
                      
                      <div
                        className={`max-w-[80%] rounded-lg p-3 ${
                          message.role === 'user'
                            ? 'bg-primary text-primary-foreground'
                            : 'bg-muted'
                        }`}
                      >
                        <div className="whitespace-pre-wrap text-sm">
                          {message.content}
                        </div>
                        <div className="text-xs opacity-70 mt-1">
                          {formatTime(message.createdAt)}
                          {message.responseTimeMs && (
                            <span className="ml-2">({message.responseTimeMs}ms)</span>
                          )}
                        </div>
                      </div>
                      
                      {message.role === 'user' && (
                        <Avatar className="h-8 w-8">
                          <AvatarFallback>
                            <User className="h-4 w-4" />
                          </AvatarFallback>
                        </Avatar>
                      )}
                    </div>
                  ))}
                  
                  {loading && (
                    <div className="flex gap-3 justify-start">
                      <Avatar className="h-8 w-8">
                        <AvatarFallback>
                          <Bot className="h-4 w-4" />
                        </AvatarFallback>
                      </Avatar>
                      <div className="bg-muted rounded-lg p-3">
                        <Loader2 className="h-4 w-4 animate-spin" />
                      </div>
                    </div>
                  )}
                  
                  <div ref={messagesEndRef} />
                </div>
              </ScrollArea>

              {/* Message Input */}
              <div className="border-t p-4">
                <div className="flex gap-2">
                  <Input
                    placeholder="Ask questions across your selected documents..."
                    value={newMessage}
                    onChange={(e) => setNewMessage(e.target.value)}
                    onKeyPress={handleKeyPress}
                    disabled={loading}
                    className="flex-1"
                  />
                  <Button 
                    onClick={sendMessage} 
                    disabled={loading || !newMessage.trim()}
                    size="icon"
                  >
                    {loading ? (
                      <Loader2 className="h-4 w-4 animate-spin" />
                    ) : (
                      <Send className="h-4 w-4" />
                    )}
                  </Button>
                </div>
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
