#!/usr/bin/env python3
"""
Simple Embedding Service for Multimodal RAG Agent

This service provides embeddings using TF-IDF vectorization.
No complex dependencies required!
"""

from flask import Flask, request, jsonify
from sklearn.feature_extraction.text import TfidfVectorizer
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
import logging
import json

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Initialize Flask app
app = Flask(__name__)

# Global variables
vectorizer = TfidfVectorizer(max_features=1000, stop_words='english', ngram_range=(1, 2))
document_vectors = None
document_texts = []
document_ids = []

@app.route('/health', methods=['GET'])
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'service': 'embedding-service',
        'version': '1.0.0',
        'documents_indexed': len(document_texts)
    })

@app.route('/embed', methods=['POST'])
def embed_text():
    """Generate embeddings for text"""
    try:
        data = request.get_json()
        if not data or 'text' not in data:
            return jsonify({'error': 'Text is required'}), 400
        
        text = data['text']
        if not text.strip():
            return jsonify({'error': 'Text cannot be empty'}), 400
        
        # Create a simple embedding using TF-IDF
        temp_vectorizer = TfidfVectorizer(max_features=384, stop_words='english')
        temp_vectorizer.fit([text])
        embedding = temp_vectorizer.transform([text]).toarray()[0]
        
        # Normalize the embedding
        norm = np.linalg.norm(embedding)
        if norm > 0:
            embedding = embedding / norm
        
        return jsonify({
            'embedding': embedding.tolist(),
            'dimension': len(embedding),
            'text_length': len(text)
        })
    
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        return jsonify({'error': 'Failed to generate embedding'}), 500

@app.route('/index', methods=['POST'])
def index_document():
    """Index a document for similarity search"""
    global document_vectors, document_texts, document_ids, vectorizer
    
    try:
        data = request.get_json()
        if not data or 'text' not in data or 'id' not in data:
            return jsonify({'error': 'Text and ID are required'}), 400
        
        text = data['text']
        doc_id = data['id']
        
        if not text.strip():
            return jsonify({'error': 'Text cannot be empty'}), 400
        
        # Add to our document collection
        document_texts.append(text)
        document_ids.append(doc_id)
        
        # Re-fit the vectorizer with all documents
        document_vectors = vectorizer.fit_transform(document_texts)
        
        logger.info(f"Indexed document {doc_id}, total documents: {len(document_texts)}")
        
        return jsonify({
            'message': 'Document indexed successfully',
            'document_id': doc_id,
            'total_documents': len(document_texts)
        })
    
    except Exception as e:
        logger.error(f"Error indexing document: {str(e)}")
        return jsonify({'error': 'Failed to index document'}), 500

@app.route('/search', methods=['POST'])
def search_similar():
    """Search for similar documents"""
    global document_vectors, document_texts, document_ids, vectorizer
    
    try:
        data = request.get_json()
        if not data or 'query' not in data:
            return jsonify({'error': 'Query is required'}), 400
        
        query = data['query']
        top_k = data.get('top_k', 5)
        
        if not query.strip():
            return jsonify({'error': 'Query cannot be empty'}), 400
        
        if document_vectors is None or len(document_texts) == 0:
            return jsonify({
                'results': [],
                'message': 'No documents indexed yet'
            })
        
        # Transform query using the fitted vectorizer
        query_vector = vectorizer.transform([query])
        
        # Calculate similarities
        similarities = cosine_similarity(query_vector, document_vectors)[0]
        
        # Get top-k results
        top_indices = np.argsort(similarities)[::-1][:top_k]
        
        results = []
        for idx in top_indices:
            if similarities[idx] > 0:  # Only include non-zero similarities
                results.append({
                    'document_id': document_ids[idx],
                    'text': document_texts[idx][:200] + '...' if len(document_texts[idx]) > 200 else document_texts[idx],
                    'similarity': float(similarities[idx])
                })
        
        return jsonify({
            'results': results,
            'query': query,
            'total_documents': len(document_texts)
        })
    
    except Exception as e:
        logger.error(f"Error searching documents: {str(e)}")
        return jsonify({'error': 'Failed to search documents'}), 500

@app.route('/delete', methods=['POST'])
def delete_document():
    """Delete a document from the index"""
    global document_vectors, document_texts, document_ids, vectorizer
    
    try:
        data = request.get_json()
        if not data or 'id' not in data:
            return jsonify({'error': 'Document ID is required'}), 400
        
        doc_id = data['id']
        
        if doc_id in document_ids:
            idx = document_ids.index(doc_id)
            document_ids.pop(idx)
            document_texts.pop(idx)
            
            # Re-fit vectorizer if documents remain
            if document_texts:
                document_vectors = vectorizer.fit_transform(document_texts)
            else:
                document_vectors = None
            
            logger.info(f"Deleted document {doc_id}, remaining documents: {len(document_texts)}")
            
            return jsonify({
                'message': 'Document deleted successfully',
                'document_id': doc_id,
                'remaining_documents': len(document_texts)
            })
        else:
            return jsonify({'error': 'Document not found'}), 404
    
    except Exception as e:
        logger.error(f"Error deleting document: {str(e)}")
        return jsonify({'error': 'Failed to delete document'}), 500

@app.route('/status', methods=['GET'])
def get_status():
    """Get service status"""
    return jsonify({
        'service': 'embedding-service',
        'status': 'running',
        'documents_indexed': len(document_texts),
        'vectorizer_features': vectorizer.max_features,
        'version': '1.0.0'
    })

if __name__ == '__main__':
    logger.info("Starting Simple Embedding Service...")
    logger.info("Service will be available at http://localhost:8001")
    
    # Start the Flask app
    app.run(
        host='0.0.0.0',
        port=8001,
        debug=False,
        threaded=True
    )
