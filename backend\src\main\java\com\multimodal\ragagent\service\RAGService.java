package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RAGService {
    
    private static final Logger logger = LoggerFactory.getLogger(RAGService.class);
    
    @Autowired
    private VectorService vectorService;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private GroqService groqService;
    
    @Value("${rag.max-context-length:4000}")
    private int maxContextLength;
    
    public RAGResponse queryDocument(Long documentId, String question, User user) {
        try {
            // Verify user has access to the document
            Document document = documentRepository.findByIdAndUser(documentId, user)
                    .orElseThrow(() -> new RuntimeException("Document not found or access denied"));

            // Always use Groq AI for ChatGPT-4 level responses
            logger.info("Processing RAG query with Groq AI for document: {}", document.getOriginalFilename());

            if (vectorService.isVectorDatabaseAvailable()) {
                // Use vector search for enhanced context
                List<DocumentChunk> relevantChunks = vectorService.searchSimilarChunks(question, 8);

                // Filter chunks to only include those from the specified document
                List<DocumentChunk> documentChunks = relevantChunks.stream()
                        .filter(chunk -> chunk.getDocument().getId().equals(documentId))
                        .collect(Collectors.toList());

                if (!documentChunks.isEmpty()) {
                    String context = buildContext(documentChunks);
                    String response = groqService.generateChatGPTLevelResponse(question, context, document.getOriginalFilename());

                    logger.info("Vector-enhanced RAG query processed for document: {} with {} relevant chunks",
                               document.getOriginalFilename(), documentChunks.size());

                    return new RAGResponse(response, documentChunks, true);
                }
            }

            // Use full document content for comprehensive analysis
            String documentContent = document.getExtractedText();
            if (documentContent == null || documentContent.trim().isEmpty()) {
                return new RAGResponse(
                    "I apologize, but this document hasn't been processed yet or contains no readable text. Please try uploading the document again or ensure it contains text content.",
                    null,
                    false
                );
            }

            // Generate ChatGPT-4 level response using full document
            String response = groqService.generateChatGPTLevelResponse(question, documentContent, document.getOriginalFilename());

            logger.info("Full-document RAG query processed for: {}", document.getOriginalFilename());

            return new RAGResponse(response, null, true);

        } catch (Exception e) {
            logger.error("Failed to process RAG query: {}", e.getMessage());
            return new RAGResponse(
                "I encountered an issue while analyzing your document. Please try rephrasing your question or try again in a moment.",
                null,
                false
            );
        }
    }
    
    public RAGResponse queryAllDocuments(String question, User user) {
        try {
            logger.info("Processing multi-document RAG query with Groq AI for user: {}", user.getUsername());

            if (vectorService.isVectorDatabaseAvailable()) {
                // Search for relevant chunks across all user's documents
                List<DocumentChunk> relevantChunks = vectorService.searchSimilarChunks(question, 12);

                // Filter chunks to only include those from user's documents
                List<DocumentChunk> userChunks = relevantChunks.stream()
                        .filter(chunk -> chunk.getDocument().getUser().getId().equals(user.getId()))
                        .collect(Collectors.toList());

                if (!userChunks.isEmpty()) {
                    // Build context from relevant chunks
                    String context = buildContext(userChunks);
                    String response = groqService.generateChatGPTLevelResponse(question, context, "your document collection");

                    logger.info("Vector-enhanced multi-document RAG query processed with {} relevant chunks", userChunks.size());

                    return new RAGResponse(response, userChunks, true);
                }
            }

            // Fallback: Get all user documents and search through them
            List<Document> userDocuments = documentRepository.findByUser(user);

            if (userDocuments.isEmpty()) {
                return new RAGResponse(
                    "You haven't uploaded any documents yet. Please upload some documents first to use the RAG system.",
                    null,
                    true
                );
            }

            // Combine content from multiple documents for comprehensive analysis
            StringBuilder combinedContent = new StringBuilder();

            for (Document doc : userDocuments.subList(0, Math.min(5, userDocuments.size()))) { // Limit to 5 most recent
                String content = doc.getExtractedText();
                if (content != null && !content.trim().isEmpty()) {
                    combinedContent.append("=== ").append(doc.getOriginalFilename()).append(" ===\n");
                    combinedContent.append(content.length() > 2000 ? content.substring(0, 2000) + "...\n\n" : content + "\n\n");
                }
            }

            if (combinedContent.length() == 0) {
                return new RAGResponse(
                    "Your documents appear to be empty or haven't been processed yet. Please try re-uploading your documents.",
                    null,
                    false
                );
            }

            // Generate comprehensive response across all documents
            String response = groqService.generateChatGPTLevelResponse(
                question + "\n\nNote: Please analyze across all the provided documents and mention which documents contain relevant information.",
                combinedContent.toString(),
                "your document collection"
            );

            logger.info("Multi-document RAG query processed across {} documents", userDocuments.size());

            return new RAGResponse(response, null, true);

        } catch (Exception e) {
            logger.error("Failed to process multi-document RAG query: {}", e.getMessage());
            return new RAGResponse(
                "I encountered an issue while analyzing your documents. Please try rephrasing your question or try again in a moment.",
                null,
                false
            );
        }
    }
    
    private String buildContext(List<DocumentChunk> chunks) {
        StringBuilder context = new StringBuilder();
        int currentLength = 0;
        
        for (DocumentChunk chunk : chunks) {
            String chunkText = chunk.getContent();
            
            // Add document context
            String chunkWithContext = String.format(
                "[From: %s]\n%s\n\n",
                chunk.getDocument().getOriginalFilename(),
                chunkText
            );
            
            if (currentLength + chunkWithContext.length() > maxContextLength) {
                break;
            }
            
            context.append(chunkWithContext);
            currentLength += chunkWithContext.length();
        }
        
        return context.toString();
    }
    

    
    public boolean isRAGAvailable() {
        return vectorService.isVectorDatabaseAvailable();
    }
    
    public String getRAGStatus() {
        return vectorService.getVectorDatabaseStatus();
    }
    
    public static class RAGResponse {
        private String answer;
        private List<DocumentChunk> sourceChunks;
        private boolean success;
        
        public RAGResponse(String answer, List<DocumentChunk> sourceChunks, boolean success) {
            this.answer = answer;
            this.sourceChunks = sourceChunks;
            this.success = success;
        }
        
        // Getters and setters
        public String getAnswer() {
            return answer;
        }
        
        public void setAnswer(String answer) {
            this.answer = answer;
        }
        
        public List<DocumentChunk> getSourceChunks() {
            return sourceChunks;
        }
        
        public void setSourceChunks(List<DocumentChunk> sourceChunks) {
            this.sourceChunks = sourceChunks;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
    }

    private RAGResponse performFallbackSearch(Document document, String question) {
        try {
            // Get full document content
            String documentText = document.getExtractedText();
            if (documentText == null || documentText.trim().isEmpty()) {
                return new RAGResponse(
                    "I apologize, but this document hasn't been processed yet or contains no text content. Please try again later.",
                    null,
                    false
                );
            }

            // Use Groq AI if available for high-quality responses
            if (groqService.isConfigured()) {
                logger.info("Using Groq AI for document query: {}", question);
                String aiResponse = groqService.generateDocumentResponse(question, documentText, document.getOriginalFilename());
                return new RAGResponse(aiResponse, null, true);
            }

            // Enhanced fallback: Intelligent document analysis without external AI
            logger.info("Using enhanced fallback analysis for document query: {}", question);
            String intelligentResponse = generateIntelligentResponse(question, documentText, document.getOriginalFilename());

            return new RAGResponse(intelligentResponse, null, true);

        } catch (Exception e) {
            logger.error("Fallback search failed for document {}: {}", document.getOriginalFilename(), e.getMessage());
            return new RAGResponse(
                "I'm having trouble processing your question right now. Please try again later.",
                null,
                false
            );
        }
    }

    private String generateIntelligentResponse(String question, String documentText, String documentName) {
        try {
            // Analyze the question type
            String questionLower = question.toLowerCase();
            boolean isSummaryRequest = questionLower.contains("summary") || questionLower.contains("summarize") ||
                                    questionLower.contains("overview") || questionLower.contains("main points") ||
                                    questionLower.contains("key points") || questionLower.contains("what is this about");

            if (isSummaryRequest) {
                return generateDocumentSummary(documentText, documentName);
            }

            // For specific questions, find relevant sections
            String[] questionWords = extractKeywords(question);
            String relevantContent = findRelevantSections(documentText, questionWords);

            if (relevantContent.isEmpty()) {
                return generateGeneralDocumentInfo(documentText, documentName, question);
            }

            return generateContextualResponse(question, relevantContent, documentName);

        } catch (Exception e) {
            logger.error("Error generating intelligent response: {}", e.getMessage());
            return "I encountered an issue while analyzing the document. Please try rephrasing your question.";
        }
    }

    private String generateDocumentSummary(String documentText, String documentName) {
        // Extract key information for summary
        String[] paragraphs = documentText.split("\n\n");
        StringBuilder summary = new StringBuilder();

        summary.append("**Document Summary: ").append(documentName).append("**\n\n");

        // Analyze document structure and content
        int totalParagraphs = paragraphs.length;
        int wordCount = documentText.split("\\s+").length;

        summary.append("**Document Overview:**\n");
        summary.append("- Length: ").append(wordCount).append(" words across ").append(totalParagraphs).append(" sections\n");

        // Extract key topics and themes
        String[] keyTopics = extractKeyTopics(documentText);
        if (keyTopics.length > 0) {
            summary.append("- Key Topics: ").append(String.join(", ", keyTopics)).append("\n");
        }

        summary.append("\n**Main Content:**\n");

        // Get first few paragraphs as introduction
        int maxParagraphs = Math.min(3, paragraphs.length);
        for (int i = 0; i < maxParagraphs; i++) {
            String paragraph = paragraphs[i].trim();
            if (!paragraph.isEmpty() && paragraph.length() > 50) {
                summary.append("• ").append(paragraph.length() > 200 ?
                    paragraph.substring(0, 200) + "..." : paragraph).append("\n\n");
            }
        }

        // Add conclusion if document is long enough
        if (paragraphs.length > 5) {
            String lastParagraph = paragraphs[paragraphs.length - 1].trim();
            if (!lastParagraph.isEmpty() && lastParagraph.length() > 50) {
                summary.append("**Conclusion:**\n");
                summary.append("• ").append(lastParagraph.length() > 200 ?
                    lastParagraph.substring(0, 200) + "..." : lastParagraph).append("\n");
            }
        }

        return summary.toString();
    }

    private String[] extractKeywords(String question) {
        // Remove common stop words and extract meaningful keywords
        String[] stopWords = {"what", "how", "when", "where", "why", "who", "is", "are", "was", "were",
                             "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with"};

        String[] words = question.toLowerCase().replaceAll("[^a-zA-Z0-9\\s]", "").split("\\s+");
        return java.util.Arrays.stream(words)
                .filter(word -> word.length() > 3)
                .filter(word -> !java.util.Arrays.asList(stopWords).contains(word))
                .toArray(String[]::new);
    }

    private String findRelevantSections(String documentText, String[] keywords) {
        String[] paragraphs = documentText.split("\n\n");
        StringBuilder relevantContent = new StringBuilder();

        for (String paragraph : paragraphs) {
            String lowerParagraph = paragraph.toLowerCase();
            int matchCount = 0;

            for (String keyword : keywords) {
                if (lowerParagraph.contains(keyword.toLowerCase())) {
                    matchCount++;
                }
            }

            // Include paragraphs with multiple keyword matches
            if (matchCount >= Math.max(1, keywords.length / 2)) {
                relevantContent.append(paragraph.trim()).append("\n\n");
            }
        }

        return relevantContent.toString().trim();
    }

    private String generateGeneralDocumentInfo(String documentText, String documentName, String question) {
        String[] paragraphs = documentText.split("\n\n");
        int wordCount = documentText.split("\\s+").length;

        StringBuilder response = new StringBuilder();
        response.append("**About ").append(documentName).append(":**\n\n");
        response.append("This document contains ").append(wordCount).append(" words across ")
                .append(paragraphs.length).append(" sections. ");

        // Provide first paragraph as context
        if (paragraphs.length > 0 && !paragraphs[0].trim().isEmpty()) {
            response.append("Here's how the document begins:\n\n");
            String firstParagraph = paragraphs[0].trim();
            response.append("\"").append(firstParagraph.length() > 300 ?
                firstParagraph.substring(0, 300) + "..." : firstParagraph).append("\"\n\n");
        }

        response.append("**Regarding your question: \"").append(question).append("\"**\n\n");
        response.append("While I couldn't find specific information matching your exact question, ");
        response.append("the document covers various topics that might be related. ");
        response.append("Try asking more specific questions about the content, or request a summary to get an overview of the main topics covered.");

        return response.toString();
    }

    private String generateContextualResponse(String question, String relevantContent, String documentName) {
        StringBuilder response = new StringBuilder();

        response.append("**Based on ").append(documentName).append(":**\n\n");
        response.append("Here's the relevant information I found regarding your question: \"")
                .append(question).append("\"\n\n");

        // Split relevant content into manageable chunks
        String[] sections = relevantContent.split("\n\n");
        int maxSections = Math.min(3, sections.length);

        for (int i = 0; i < maxSections; i++) {
            String section = sections[i].trim();
            if (!section.isEmpty()) {
                response.append("**Section ").append(i + 1).append(":**\n");
                response.append(section.length() > 400 ? section.substring(0, 400) + "..." : section);
                response.append("\n\n");
            }
        }

        if (sections.length > maxSections) {
            response.append("*Note: There are ").append(sections.length - maxSections)
                    .append(" additional relevant sections in the document.*\n\n");
        }

        response.append("This information should help answer your question. ");
        response.append("If you need more specific details, please ask a more targeted question about any particular aspect.");

        return response.toString();
    }

    private String[] extractKeyTopics(String documentText) {
        // Simple topic extraction based on frequently occurring meaningful words
        String[] words = documentText.toLowerCase().replaceAll("[^a-zA-Z0-9\\s]", "").split("\\s+");
        java.util.Map<String, Integer> wordCount = new java.util.HashMap<>();

        String[] stopWords = {"the", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with", "by", "from",
                             "up", "about", "into", "through", "during", "before", "after", "above", "below", "between",
                             "is", "are", "was", "were", "be", "been", "being", "have", "has", "had", "do", "does", "did",
                             "will", "would", "could", "should", "may", "might", "must", "can", "this", "that", "these", "those"};

        for (String word : words) {
            if (word.length() > 4 && !java.util.Arrays.asList(stopWords).contains(word)) {
                wordCount.put(word, wordCount.getOrDefault(word, 0) + 1);
            }
        }

        return wordCount.entrySet().stream()
                .filter(entry -> entry.getValue() > 2) // Words that appear more than twice
                .sorted((e1, e2) -> e2.getValue().compareTo(e1.getValue()))
                .limit(5)
                .map(java.util.Map.Entry::getKey)
                .toArray(String[]::new);
    }

    private String extractRelevantContext(String documentText, String[] questionWords) {
        // Simple context extraction - find paragraphs containing question keywords
        String[] paragraphs = documentText.split("\n\n");
        StringBuilder relevantContext = new StringBuilder();

        for (String paragraph : paragraphs) {
            String lowerParagraph = paragraph.toLowerCase();
            for (String word : questionWords) {
                if (word.length() > 3 && lowerParagraph.contains(word)) {
                    relevantContext.append(paragraph).append("\n\n");
                    break;
                }
            }

            // Limit context length
            if (relevantContext.length() > maxContextLength) {
                break;
            }
        }

        return relevantContext.toString().trim();
    }

    private String generateBasicResponse(String question, String context) {
        if (context.isEmpty()) {
            return "I found some content in the document, but couldn't extract specific information to answer your question. Please try asking more specific questions about the document content.";
        }

        return String.format("Based on the document content, here's what I found:\n\n%s\n\nThis information might help answer your question about: %s",
                           context.length() > 500 ? context.substring(0, 500) + "..." : context,
                           question);
    }
}
