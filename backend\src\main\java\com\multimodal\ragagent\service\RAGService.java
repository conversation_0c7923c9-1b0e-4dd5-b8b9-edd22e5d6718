package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.DocumentChunk;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class RAGService {
    
    private static final Logger logger = LoggerFactory.getLogger(RAGService.class);
    
    @Autowired
    private VectorService vectorService;

    @Autowired
    private DocumentRepository documentRepository;

    @Autowired
    private GroqService groqService;
    
    @Value("${rag.max-context-length:4000}")
    private int maxContextLength;
    
    public RAGResponse queryDocument(Long documentId, String question, User user) {
        try {
            // Verify user has access to the document
            Document document = documentRepository.findByIdAndUser(documentId, user)
                    .orElseThrow(() -> new RuntimeException("Document not found or access denied"));
            
            if (!vectorService.isVectorDatabaseAvailable()) {
                logger.warn("Vector database not available for document query. Using fallback search for document: {}",
                           document.getOriginalFilename());

                // Fallback to simple text search without vector database
                return performFallbackSearch(document, question);
            }
            
            // Search for relevant chunks
            List<DocumentChunk> relevantChunks = vectorService.searchSimilarChunks(question, 5);
            
            // Filter chunks to only include those from the specified document
            List<DocumentChunk> documentChunks = relevantChunks.stream()
                    .filter(chunk -> chunk.getDocument().getId().equals(documentId))
                    .collect(Collectors.toList());
            
            if (documentChunks.isEmpty()) {
                return new RAGResponse(
                    "No relevant information found in the document for your question.",
                    null,
                    true
                );
            }
            
            // Build context from relevant chunks
            String context = buildContext(documentChunks);

            // Generate AI response using Groq
            String response = groqService.generateRAGResponse(question, context, document.getOriginalFilename());
            
            logger.info("RAG query processed for document: {} with {} relevant chunks", 
                       document.getOriginalFilename(), documentChunks.size());
            
            return new RAGResponse(response, documentChunks, true);
            
        } catch (Exception e) {
            logger.error("Failed to process RAG query: {}", e.getMessage());
            return new RAGResponse(
                "An error occurred while processing your question: " + e.getMessage(),
                null,
                false
            );
        }
    }
    
    public RAGResponse queryAllDocuments(String question, User user) {
        try {
            if (!vectorService.isVectorDatabaseAvailable()) {
                return new RAGResponse(
                    "Vector database is not available. Please ensure ChromaDB is running and OpenAI API key is configured.",
                    null,
                    false
                );
            }
            
            // Search for relevant chunks across all user's documents
            List<DocumentChunk> relevantChunks = vectorService.searchSimilarChunks(question, 10);
            
            // Filter chunks to only include those from user's documents
            List<DocumentChunk> userChunks = relevantChunks.stream()
                    .filter(chunk -> chunk.getDocument().getUser().getId().equals(user.getId()))
                    .collect(Collectors.toList());
            
            if (userChunks.isEmpty()) {
                return new RAGResponse(
                    "No relevant information found in your documents for this question.",
                    null,
                    true
                );
            }
            
            // Build context from relevant chunks
            String context = buildContext(userChunks);

            // Generate AI response using Groq
            String response = groqService.generateRAGResponse(question, context, "your documents");
            
            logger.info("RAG query processed across all documents with {} relevant chunks", userChunks.size());
            
            return new RAGResponse(response, userChunks, true);
            
        } catch (Exception e) {
            logger.error("Failed to process RAG query: {}", e.getMessage());
            return new RAGResponse(
                "An error occurred while processing your question: " + e.getMessage(),
                null,
                false
            );
        }
    }
    
    private String buildContext(List<DocumentChunk> chunks) {
        StringBuilder context = new StringBuilder();
        int currentLength = 0;
        
        for (DocumentChunk chunk : chunks) {
            String chunkText = chunk.getContent();
            
            // Add document context
            String chunkWithContext = String.format(
                "[From: %s]\n%s\n\n",
                chunk.getDocument().getOriginalFilename(),
                chunkText
            );
            
            if (currentLength + chunkWithContext.length() > maxContextLength) {
                break;
            }
            
            context.append(chunkWithContext);
            currentLength += chunkWithContext.length();
        }
        
        return context.toString();
    }
    

    
    public boolean isRAGAvailable() {
        return vectorService.isVectorDatabaseAvailable();
    }
    
    public String getRAGStatus() {
        return vectorService.getVectorDatabaseStatus();
    }
    
    public static class RAGResponse {
        private String answer;
        private List<DocumentChunk> sourceChunks;
        private boolean success;
        
        public RAGResponse(String answer, List<DocumentChunk> sourceChunks, boolean success) {
            this.answer = answer;
            this.sourceChunks = sourceChunks;
            this.success = success;
        }
        
        // Getters and setters
        public String getAnswer() {
            return answer;
        }
        
        public void setAnswer(String answer) {
            this.answer = answer;
        }
        
        public List<DocumentChunk> getSourceChunks() {
            return sourceChunks;
        }
        
        public void setSourceChunks(List<DocumentChunk> sourceChunks) {
            this.sourceChunks = sourceChunks;
        }
        
        public boolean isSuccess() {
            return success;
        }
        
        public void setSuccess(boolean success) {
            this.success = success;
        }
    }

    private RAGResponse performFallbackSearch(Document document, String question) {
        try {
            // Simple keyword-based search in document content
            String documentText = document.getExtractedText();
            if (documentText == null || documentText.trim().isEmpty()) {
                return new RAGResponse(
                    "I apologize, but this document hasn't been processed yet or contains no text content. Please try again later.",
                    null,
                    false
                );
            }

            // Simple keyword matching
            String[] questionWords = question.toLowerCase().split("\\s+");
            String lowerDocText = documentText.toLowerCase();

            boolean hasRelevantContent = false;
            for (String word : questionWords) {
                if (word.length() > 3 && lowerDocText.contains(word)) {
                    hasRelevantContent = true;
                    break;
                }
            }

            if (!hasRelevantContent) {
                return new RAGResponse(
                    "I couldn't find relevant information about your question in this document. Try rephrasing your question or asking about different topics covered in the document.",
                    null,
                    true
                );
            }

            // Extract relevant context (simple approach)
            String context = extractRelevantContext(documentText, questionWords);

            // Generate response using Groq if available, otherwise provide basic response
            String answer;
            if (groqService.isConfigured()) {
                answer = groqService.generateResponse(question, context);
            } else {
                answer = generateBasicResponse(question, context);
            }

            return new RAGResponse(answer, null, true);

        } catch (Exception e) {
            logger.error("Fallback search failed for document {}: {}", document.getOriginalFilename(), e.getMessage());
            return new RAGResponse(
                "I'm having trouble processing your question right now. Please try again later.",
                null,
                false
            );
        }
    }

    private String extractRelevantContext(String documentText, String[] questionWords) {
        // Simple context extraction - find paragraphs containing question keywords
        String[] paragraphs = documentText.split("\n\n");
        StringBuilder relevantContext = new StringBuilder();

        for (String paragraph : paragraphs) {
            String lowerParagraph = paragraph.toLowerCase();
            for (String word : questionWords) {
                if (word.length() > 3 && lowerParagraph.contains(word)) {
                    relevantContext.append(paragraph).append("\n\n");
                    break;
                }
            }

            // Limit context length
            if (relevantContext.length() > maxContextLength) {
                break;
            }
        }

        return relevantContext.toString().trim();
    }

    private String generateBasicResponse(String question, String context) {
        if (context.isEmpty()) {
            return "I found some content in the document, but couldn't extract specific information to answer your question. Please try asking more specific questions about the document content.";
        }

        return String.format("Based on the document content, here's what I found:\n\n%s\n\nThis information might help answer your question about: %s",
                           context.length() > 500 ? context.substring(0, 500) + "..." : context,
                           question);
    }
}
