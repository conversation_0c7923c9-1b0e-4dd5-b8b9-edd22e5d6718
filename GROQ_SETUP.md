# 🚀 Groq API Setup Guide - FREE AI Power for Your RAG System

## Why Groq?
- ✅ **FREE** - No credit card required
- ✅ **FAST** - Lightning-fast inference
- ✅ **POWERFUL** - Llama 3 models (8B and 70B)
- ✅ **GENEROUS LIMITS** - 30 requests/minute free tier

## 🔧 Quick Setup (5 minutes)

### Step 1: Get Your Free API Key

1. **Visit**: https://console.groq.com/
2. **Sign up** with your email (free account)
3. **Go to API Keys** section
4. **Create New API Key**
5. **Copy the key** (starts with `gsk_...`)

### Step 2: Configure Your System

**Option A: Environment Variable (Recommended)**
```bash
# Windows (PowerShell)
$env:GROQ_API_KEY="your-groq-api-key-here"

# Windows (Command Prompt)
set GROQ_API_KEY=your-groq-api-key-here

# Linux/Mac
export GROQ_API_KEY="your-groq-api-key-here"
```

**Option B: Update application.yml**
```yaml
groq:
  api:
    key: your-groq-api-key-here
```

### Step 3: Restart Backend
```bash
cd backend
./mvnw.cmd spring-boot:run
```

## 🎯 What You'll Get

### Before (Current State)
- ❌ Basic keyword matching
- ❌ Simple text extraction
- ❌ Limited document understanding

### After (With Groq API)
- ✅ **AI-Powered Summaries** - Intelligent document analysis
- ✅ **Natural Language Responses** - Human-like answers
- ✅ **Context Understanding** - Grasps document meaning
- ✅ **Multiple Question Types** - Summaries, specific queries, analysis

## 🧪 Test Your Setup

1. **Upload a document** (PDF, DOC, TXT)
2. **Ask**: "Summarize this document"
3. **Ask**: "What are the main points?"
4. **Ask**: "Explain the key concepts"

You should get detailed, intelligent responses instead of basic text matching!

## 🔍 Verify It's Working

Check the backend logs for:
```
Using Groq AI for document query: [your question]
```

Instead of:
```
Using enhanced fallback analysis for document query: [your question]
```

## 🆓 Free Tier Limits
- **30 requests/minute**
- **6,000 requests/day**
- **No credit card required**
- **Perfect for development and testing**

## 🚀 Ready to Go!

Once configured, your RAG system will provide:
- **Professional summaries**
- **Detailed analysis**
- **Contextual answers**
- **High-quality responses**

**All powered by FREE Groq AI!** 🎉
