package com.multimodal.ragagent.dto;

import com.multimodal.ragagent.entity.Note;

import java.time.LocalDateTime;

public class NoteDTO {
    private Long id;
    private String title;
    private String content;
    private Note.NoteType noteType;
    private Note.NoteStatus status;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
    private Long userId;
    private String username;
    private Long documentId;
    private String documentFilename;

    // Default constructor
    public NoteDTO() {}

    // Constructor from Note entity
    public NoteDTO(Note note) {
        this.id = note.getId();
        this.title = note.getTitle();
        this.content = note.getContent();
        this.noteType = note.getNoteType();
        this.status = note.getStatus();
        this.createdAt = note.getCreatedAt();
        this.updatedAt = note.getUpdatedAt();
        if (note.getUser() != null) {
            this.userId = note.getUser().getId();
            this.username = note.getUser().getUsername();
        }
        if (note.getDocument() != null) {
            this.documentId = note.getDocument().getId();
            this.documentFilename = note.getDocument().getOriginalFilename();
        }
    }

    // Getters and setters
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTitle() {
        return title;
    }

    public void setTitle(String title) {
        this.title = title;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Note.NoteType getNoteType() {
        return noteType;
    }

    public void setNoteType(Note.NoteType noteType) {
        this.noteType = noteType;
    }

    public Note.NoteStatus getStatus() {
        return status;
    }

    public void setStatus(Note.NoteStatus status) {
        this.status = status;
    }

    public LocalDateTime getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }

    public LocalDateTime getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(LocalDateTime updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Long getUserId() {
        return userId;
    }

    public void setUserId(Long userId) {
        this.userId = userId;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public Long getDocumentId() {
        return documentId;
    }

    public void setDocumentId(Long documentId) {
        this.documentId = documentId;
    }

    public String getDocumentFilename() {
        return documentFilename;
    }

    public void setDocumentFilename(String documentFilename) {
        this.documentFilename = documentFilename;
    }
}
