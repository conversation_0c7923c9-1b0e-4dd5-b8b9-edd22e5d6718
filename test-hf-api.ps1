Write-Host "Testing Hugging Face API directly..."

$apiKey = "*************************************"
$model = "sentence-transformers/all-MiniLM-L6-v2"

$headers = @{
    'Authorization' = "Bearer $apiKey"
    'Content-Type' = 'application/json'
}

$body = @{
    inputs = "This is a test sentence"
    options = @{
        wait_for_model = $true
    }
} | ConvertTo-Json

# Test different API endpoints
$endpoints = @(
    "https://api-inference.huggingface.co/models/$model",
    "https://api-inference.huggingface.co/pipeline/feature-extraction/$model"
)

foreach ($endpoint in $endpoints) {
    Write-Host "`nTesting endpoint: $endpoint" -ForegroundColor Cyan
    
    try {
        $response = Invoke-RestMethod -Uri $endpoint -Method POST -Headers $headers -Body $body -TimeoutSec 30
        Write-Host "SUCCESS!" -ForegroundColor Green
        Write-Host "Response type:" $response.GetType().Name
        if ($response -is [array]) {
            Write-Host "Array length:" $response.Length
            if ($response.Length -gt 0 -and $response[0] -is [array]) {
                Write-Host "Embedding dimensions:" $response[0].Length
                Write-Host "First 5 values:" ($response[0][0..4] -join ", ")
            }
        }
        break
        
    } catch {
        Write-Host "ERROR:" -ForegroundColor Red
        Write-Host $_.Exception.Message
        
        if ($_.Exception.Response) {
            Write-Host "Status Code:" $_.Exception.Response.StatusCode
            try {
                $errorStream = $_.Exception.Response.GetResponseStream()
                $reader = New-Object System.IO.StreamReader($errorStream)
                $errorBody = $reader.ReadToEnd()
                Write-Host "Error Body:" $errorBody
            } catch {
                Write-Host "Could not read error body"
            }
        }
    }
}
