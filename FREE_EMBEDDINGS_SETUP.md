# 🆓 FREE Embeddings Setup Guide

This guide shows you how to set up **completely free** embeddings for the RAG system. No API keys, no payments required!

## 🎯 Overview

Instead of using paid OpenAI embeddings, we provide a **free local embedding service** using Sentence Transformers that runs on your machine.

### ✅ Benefits:
- **100% Free** - No API costs
- **Privacy** - All processing happens locally
- **No API Keys** - No registration required
- **Good Quality** - Uses state-of-the-art models
- **Fast** - Runs locally, no network delays

## 🚀 Quick Start

### Option 1: Docker (Recommended)

```bash
# 1. Build and run the embedding service
cd embedding-service
docker build -t local-embeddings .
docker run -p 8001:8001 local-embeddings

# 2. Start ChromaDB
docker run -p 8000:8000 chromadb/chroma

# 3. Start the main application
cd ../backend
./mvnw spring-boot:run
```

### Option 2: Python Direct

```bash
# 1. Install Python dependencies
cd embedding-service
pip install -r requirements.txt

# 2. Start the embedding service
python app.py

# 3. In another terminal, start ChromaDB
docker run -p 8000:8000 chromadb/chroma

# 4. In another terminal, start the main application
cd ../backend
./mvnw spring-boot:run
```

## 📋 Detailed Setup

### Step 1: Start the Free Embedding Service

#### Using Docker:
```bash
cd embedding-service
docker build -t local-embeddings .
docker run -p 8001:8001 local-embeddings
```

#### Using Python:
```bash
cd embedding-service
pip install -r requirements.txt
python app.py
```

You should see:
```
🚀 FREE LOCAL EMBEDDING SERVICE
Model: sentence-transformers/all-MiniLM-L6-v2
Port: 8001
✅ Model loaded successfully!
🌐 Starting server at http://localhost:8001
```

### Step 2: Start ChromaDB

```bash
docker run -p 8000:8000 chromadb/chroma
```

### Step 3: Configure the Application

The application is already configured to use free embeddings by default:

```yaml
# In application.yml
embedding:
  provider: local  # Uses free local service
  service:
    host: localhost
    port: 8001
```

### Step 4: Start the Main Application

```bash
cd backend
./mvnw spring-boot:run
```

You should see:
```
Vector database status: Local embedding service available
Vector database is ready for use
```

## 🧪 Testing the Setup

### 1. Test Embedding Service
```bash
curl -X POST http://localhost:8001/embed \
  -H "Content-Type: application/json" \
  -d '{"text": "Hello world"}'
```

### 2. Upload a Document
- Go to http://localhost:3000/upload
- Upload any document
- It will be automatically indexed with free embeddings

### 3. Query Documents
```bash
curl -X POST http://localhost:8081/api/rag/query \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -d '{"documentId": 1, "question": "What is this document about?"}'
```

## ⚙️ Configuration Options

### Change Embedding Model

You can use different models by setting the `MODEL_NAME` environment variable:

```bash
# Available models:
export MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2      # Default, fast
export MODEL_NAME=sentence-transformers/all-mpnet-base-v2     # Better quality
export MODEL_NAME=sentence-transformers/paraphrase-MiniLM-L6-v2  # Paraphrase detection
```

### Performance Tuning

For better performance on CPU:
```bash
export OMP_NUM_THREADS=4  # Adjust based on your CPU cores
```

For GPU support (if available):
```bash
pip install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
```

## 🔄 Fallback to OpenAI (Optional)

If you want to use OpenAI as a fallback:

1. Set your API key:
```bash
export OPENAI_API_KEY=your-api-key-here
```

2. Change the provider:
```yaml
embedding:
  provider: openai  # Will use paid OpenAI service
```

## 📊 Model Comparison

| Model | Dimensions | Speed | Quality | Use Case |
|-------|------------|-------|---------|----------|
| all-MiniLM-L6-v2 | 384 | Fast | Good | General purpose |
| all-mpnet-base-v2 | 768 | Medium | Better | Higher quality |
| paraphrase-MiniLM-L6-v2 | 384 | Fast | Good | Paraphrase detection |

## 🐛 Troubleshooting

### Embedding Service Won't Start
```bash
# Check Python version (needs 3.7+)
python --version

# Install dependencies
pip install -r requirements.txt

# Check port availability
netstat -an | grep 8001
```

### ChromaDB Connection Issues
```bash
# Check if ChromaDB is running
docker ps | grep chroma

# Check ChromaDB logs
docker logs <chromadb-container-id>

# Test ChromaDB directly
curl http://localhost:8000/api/v1/collections
```

### Application Can't Connect
```bash
# Check application logs for:
# "Local embedding service available" - ✅ Good
# "Using mock embeddings" - ⚠️ Service not available

# Test connectivity
curl http://localhost:8001/health
```

## 🎉 Success Indicators

When everything is working, you should see:

1. **Embedding Service**: `✅ Model loaded successfully!`
2. **ChromaDB**: `Connect to Chroma at: http://localhost:8000`
3. **Main App**: `Vector database is ready for use`
4. **Document Upload**: Documents get indexed automatically
5. **RAG Queries**: Return relevant results with source chunks

## 💡 Tips

1. **First Run**: The embedding model will be downloaded (~90MB) on first start
2. **Memory**: The service uses ~500MB RAM when loaded
3. **Speed**: Local embeddings are typically faster than API calls
4. **Quality**: Sentence Transformers provide excellent quality for most use cases
5. **Offline**: Works completely offline once models are downloaded

## 🆚 Free vs Paid Comparison

| Feature | Free (Local) | Paid (OpenAI) |
|---------|--------------|---------------|
| Cost | $0 | ~$0.0001/1K tokens |
| Privacy | 100% local | Sent to OpenAI |
| Speed | Fast (local) | Network dependent |
| Quality | Very good | Excellent |
| Setup | Requires Python | Just API key |
| Offline | Yes | No |

The free local embeddings provide excellent quality for most use cases and are perfect for development, testing, and production use!
