FROM python:3.9-slim

WORKDIR /app

# Install system dependencies
RUN apt-get update && apt-get install -y \
    gcc \
    g++ \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app.py .

# Expose port
EXPOSE 8001

# Set environment variables
ENV MODEL_NAME=sentence-transformers/all-MiniLM-L6-v2
ENV PYTHONUNBUFFERED=1

# Run the application
CMD ["python", "app.py"]
