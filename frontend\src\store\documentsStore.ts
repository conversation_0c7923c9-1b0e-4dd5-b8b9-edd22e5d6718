import { create } from 'zustand';
import { documentsAPI } from '@/lib/api';

interface Document {
  id: number;
  originalFilename: string;
  fileSize: number;
  contentType: string;
  fileType: string;
  processingStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  createdAt: string;
  updatedAt: string;
}

interface DocumentsState {
  documents: Document[];
  currentDocument: Document | null;
  isLoading: boolean;
  uploadProgress: number;
  setDocuments: (documents: Document[]) => void;
  addDocument: (document: Document) => void;
  updateDocument: (id: number, updates: Partial<Document>) => void;
  removeDocument: (id: number) => void;
  setCurrentDocument: (document: Document | null) => void;
  setLoading: (loading: boolean) => void;
  setUploadProgress: (progress: number) => void;
  fetchDocuments: () => Promise<void>;
  deleteDocument: (id: number) => Promise<boolean>;
}

export const useDocumentsStore = create<DocumentsState>((set, get) => ({
  documents: [],
  currentDocument: null,
  isLoading: false,
  uploadProgress: 0,

  setDocuments: (documents: Document[]) => {
    set({ documents });
  },

  addDocument: (document: Document) => {
    set((state) => ({
      documents: [document, ...state.documents],
    }));
  },

  updateDocument: (id: number, updates: Partial<Document>) => {
    set((state) => ({
      documents: state.documents.map((doc) =>
        doc.id === id ? { ...doc, ...updates } : doc
      ),
      currentDocument:
        state.currentDocument?.id === id
          ? { ...state.currentDocument, ...updates }
          : state.currentDocument,
    }));
  },

  removeDocument: (id: number) => {
    set((state) => ({
      documents: state.documents.filter((doc) => doc.id !== id),
      currentDocument:
        state.currentDocument?.id === id ? null : state.currentDocument,
    }));
  },

  deleteDocument: async (id: number) => {
    try {
      await documentsAPI.delete(id);
      get().removeDocument(id);
      return true;
    } catch (error) {
      console.error('Failed to delete document:', error);
      throw error;
    }
  },

  setCurrentDocument: (document: Document | null) => {
    set({ currentDocument: document });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setUploadProgress: (progress: number) => {
    set({ uploadProgress: progress });
  },

  fetchDocuments: async () => {
    try {
      set({ isLoading: true });
      const response = await documentsAPI.getAll();
      set({ documents: response.data.content || response.data || [] });
    } catch (error) {
      console.error('Failed to fetch documents:', error);
      set({ documents: [] });
    } finally {
      set({ isLoading: false });
    }
  },
}));
