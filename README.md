# 🚀 Multimodal RAG Agent

A **comprehensive Retrieval-Augmented Generation (RAG) system** that processes multiple document formats and provides intelligent document analysis, advanced note generation, and conversational AI capabilities with **ChatGPT-4 level performance**.

## ✨ Key Features

### 📄 **Advanced Document Processing**
- **15+ File Formats**: PDF, DOC, DOCX, PPT, PPTX, XLS, XLSX, CSV, TXT, RTF, ODT, HTML, XML, JSON, Markdown
- **Intelligent Text Extraction**: Apache POI, PDFBox, and Apache Tika integration
- **Advanced Table Detection**: Multi-strategy table extraction from PDFs
- **OCR Capabilities**: Tesseract integration for image text extraction
- **Processing Statistics**: Word count, page estimation, metadata collection

### 🧠 **AI-Powered Notes Generation**
- **10 Specialized Templates**: 
  - **Basic**: Detailed Notes, Medium Level, Quick Revision
  - **Advanced**: Executive Summary, Key Points, Structured Outline
  - **Study Aids**: Flashcards, Q&A Study Guide, Comprehensive Study Guide
  - **Visual**: Mind Maps with relationship mapping
- **Professional PDF Export**: iText7 integration with markdown formatting
- **Groq AI Integration**: ChatGPT-4 level intelligent content generation
- **Template Management**: Categorized templates with rich metadata

### 🔍 **Vector RAG System**
- **ChromaDB Integration**: High-performance vector database
- **Intelligent Embeddings**: Hugging Face + Local fallback system
- **Semantic Search**: Context-aware document retrieval
- **Real-time Indexing**: Automatic document vectorization

### 💬 **Advanced Chat Interface**
- **Document-Aware Conversations**: Chat with your documents
- **Context Preservation**: Maintains conversation history
- **Multi-document Support**: Query across multiple documents
- **Groq AI Backend**: Fast, intelligent responses

### 🔐 **Enterprise Security**
- **JWT Authentication**: Secure token-based auth
- **User Isolation**: Complete data separation
- **Role-based Access**: Granular permissions
- **API Security**: CORS and security headers

### 🎨 **Modern User Interface**
- **React 18 + TypeScript**: Type-safe, modern frontend
- **Tailwind CSS**: Beautiful, responsive design
- **Real-time Updates**: Live status monitoring
- **Progress Tracking**: Document processing indicators

## 🏗️ **Architecture**

### **Backend Stack**
- **Java 17** with Spring Boot 3.2
- **PostgreSQL** for data persistence
- **ChromaDB** for vector storage
- **Apache Tika/POI/PDFBox** for document processing
- **Groq API** for AI-powered features
- **Tesseract OCR** for image text extraction
- **iText7** for PDF generation

### **Frontend Stack**
- **React 18** with TypeScript
- **Tailwind CSS** for styling
- **Axios** for API communication
- **React Router** for navigation
- **Real-time status updates**

### **AI & ML Stack**
- **Groq API**: ChatGPT-4 level language model
- **Hugging Face**: Sentence transformers for embeddings
- **ChromaDB**: Vector similarity search
- **Local Embeddings**: Fallback embedding service

## 🚀 **Quick Start**

### **Prerequisites**
- Java 17+
- Node.js 18+
- PostgreSQL 13+
- Docker (for ChromaDB)

### **1. Clone Repository**
```bash
git clone https://github.com/Saket2713/Multi-Modal-Rag.git
cd Multi-Modal-Rag
```

### **2. Start Vector Database**
```bash
docker-compose -f docker-compose.chromadb.yml up -d
```

### **3. Configure Backend**
```bash
cd backend

# Set environment variables
export GROQ_API_KEY=your_groq_api_key
export HUGGINGFACE_API_KEY=your_huggingface_api_key

# Run the application
./mvnw spring-boot:run
```

### **4. Configure Frontend**
```bash
cd frontend
npm install
npm start
```

### **5. Access Application**
- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8081
- **ChromaDB**: http://localhost:8000

## 📚 **API Documentation**

### **Authentication**
```http
POST /api/auth/register    # User registration
POST /api/auth/login       # User login
GET  /api/auth/profile     # Get user profile
```

### **Document Management**
```http
POST   /api/documents/upload        # Upload documents
GET    /api/documents               # List user documents
GET    /api/documents/{id}          # Get document details
GET    /api/documents/{id}/status   # Get processing status
DELETE /api/documents/{id}          # Delete document
```

### **Advanced Notes System**
```http
POST /api/notes/generate            # Generate AI notes
GET  /api/notes                     # List user notes
GET  /api/notes/types               # Get available note types
GET  /api/notes/templates           # Get note templates
GET  /api/notes/{id}/export/pdf     # Export note as PDF
```

### **Chat & RAG**
```http
POST /api/chat/message              # Send chat message
GET  /api/chat/history              # Get chat history
GET  /api/rag/status                # Get RAG system status
```

### **System Status**
```http
GET /api/rag/status                 # Vector DB status
GET /api/notes/status               # Notes system status
GET /api/chat/statistics            # Chat statistics
```

## ⚙️ **Configuration**

### **Environment Variables**
```bash
# Database Configuration
SPRING_DATASOURCE_URL=***********************************************
SPRING_DATASOURCE_USERNAME=your_db_username
SPRING_DATASOURCE_PASSWORD=your_db_password

# AI Services
GROQ_API_KEY=your_groq_api_key                    # Required for AI features
HUGGINGFACE_API_KEY=your_huggingface_api_key      # Optional (has fallback)

# Vector Database
CHROMADB_URL=http://localhost:8000

# Security
JWT_SECRET=your_jwt_secret
JWT_EXPIRATION=86400000

# File Upload
SPRING_SERVLET_MULTIPART_MAX_FILE_SIZE=50MB
SPRING_SERVLET_MULTIPART_MAX_REQUEST_SIZE=50MB
```

### **Database Setup**
```sql
CREATE DATABASE multimodal_rag;
CREATE USER rag_user WITH PASSWORD 'your_password';
GRANT ALL PRIVILEGES ON DATABASE multimodal_rag TO rag_user;
```

## 🎯 **Usage Examples**

### **1. Document Upload & Processing**
```bash
curl -X POST http://localhost:8081/api/documents/upload \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -F "file=@document.pdf"
```

### **2. Generate Study Notes**
```bash
curl -X POST http://localhost:8081/api/notes/generate \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"documentId": 1, "noteType": "FLASHCARDS"}'
```

### **3. Chat with Documents**
```bash
curl -X POST http://localhost:8081/api/chat/message \
  -H "Authorization: Bearer YOUR_JWT_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"message": "What are the key points in this document?"}'
```

## 🔧 **Development**

### **Backend Development**
```bash
cd backend
./mvnw spring-boot:run -Dspring-boot.run.profiles=dev
```

### **Frontend Development**
```bash
cd frontend
npm run dev
```

### **Running Tests**
```bash
# Backend tests
cd backend && ./mvnw test

# Frontend tests
cd frontend && npm test
```

## 📊 **System Status**

The system provides real-time status monitoring:

- ✅ **Vector Database**: ChromaDB connection and health
- ✅ **AI Services**: Groq API and embedding services
- ✅ **Document Processing**: File processing capabilities
- ✅ **Notes Generation**: Template and AI availability
- ✅ **Chat System**: Conversation and RAG functionality

## 🤝 **Contributing**

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📄 **License**

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🙏 **Acknowledgments**

- **Groq** for high-performance AI inference
- **Hugging Face** for embedding models
- **ChromaDB** for vector database
- **Apache Foundation** for document processing libraries
- **Spring Boot** for the robust backend framework

---

**Built with ❤️ for intelligent document processing and AI-powered knowledge management.**
