package com.multimodal.ragagent.service;

import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;

@Service
public class TextChunkingService {
    
    private static final Pattern SENTENCE_PATTERN = Pattern.compile("[.!?]+\\s+");
    private static final Pattern PARAGRAPH_PATTERN = Pattern.compile("\\n\\s*\\n");
    
    public List<String> chunkText(String text, int chunkSize, int overlap) {
        if (text == null || text.trim().isEmpty()) {
            return new ArrayList<>();
        }
        
        // Clean and normalize text
        String cleanText = cleanText(text);
        
        // Try to chunk by paragraphs first
        List<String> chunks = chunkByParagraphs(cleanText, chunkSize, overlap);
        
        // If paragraphs are too large, chunk by sentences
        if (chunks.stream().anyMatch(chunk -> chunk.length() > chunkSize * 1.5)) {
            chunks = chunkBySentences(cleanText, chunkSize, overlap);
        }
        
        // If sentences are still too large, chunk by characters
        if (chunks.stream().anyMatch(chunk -> chunk.length() > chunkSize * 1.5)) {
            chunks = chunkByCharacters(cleanText, chunkSize, overlap);
        }
        
        return chunks;
    }
    
    private String cleanText(String text) {
        return text
            .replaceAll("\\r\\n", "\n")  // Normalize line endings
            .replaceAll("\\r", "\n")     // Normalize line endings
            .replaceAll("\\s+", " ")     // Normalize whitespace
            .trim();
    }
    
    private List<String> chunkByParagraphs(String text, int chunkSize, int overlap) {
        List<String> chunks = new ArrayList<>();
        String[] paragraphs = PARAGRAPH_PATTERN.split(text);
        
        StringBuilder currentChunk = new StringBuilder();
        
        for (String paragraph : paragraphs) {
            paragraph = paragraph.trim();
            if (paragraph.isEmpty()) continue;
            
            // If adding this paragraph would exceed chunk size
            if (currentChunk.length() + paragraph.length() > chunkSize && currentChunk.length() > 0) {
                chunks.add(currentChunk.toString().trim());
                
                // Start new chunk with overlap
                String overlapText = getOverlapText(currentChunk.toString(), overlap);
                currentChunk = new StringBuilder(overlapText);
                if (!overlapText.isEmpty()) {
                    currentChunk.append("\n\n");
                }
            }
            
            if (currentChunk.length() > 0) {
                currentChunk.append("\n\n");
            }
            currentChunk.append(paragraph);
        }
        
        // Add the last chunk
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }
        
        return chunks;
    }
    
    private List<String> chunkBySentences(String text, int chunkSize, int overlap) {
        List<String> chunks = new ArrayList<>();
        String[] sentences = SENTENCE_PATTERN.split(text);
        
        StringBuilder currentChunk = new StringBuilder();
        
        for (String sentence : sentences) {
            sentence = sentence.trim();
            if (sentence.isEmpty()) continue;
            
            // If adding this sentence would exceed chunk size
            if (currentChunk.length() + sentence.length() > chunkSize && currentChunk.length() > 0) {
                chunks.add(currentChunk.toString().trim());
                
                // Start new chunk with overlap
                String overlapText = getOverlapText(currentChunk.toString(), overlap);
                currentChunk = new StringBuilder(overlapText);
                if (!overlapText.isEmpty()) {
                    currentChunk.append(" ");
                }
            }
            
            if (currentChunk.length() > 0) {
                currentChunk.append(" ");
            }
            currentChunk.append(sentence);
        }
        
        // Add the last chunk
        if (currentChunk.length() > 0) {
            chunks.add(currentChunk.toString().trim());
        }
        
        return chunks;
    }
    
    private List<String> chunkByCharacters(String text, int chunkSize, int overlap) {
        List<String> chunks = new ArrayList<>();
        
        for (int i = 0; i < text.length(); i += chunkSize - overlap) {
            int end = Math.min(i + chunkSize, text.length());
            String chunk = text.substring(i, end);
            
            // Try to break at word boundaries
            if (end < text.length() && !Character.isWhitespace(text.charAt(end))) {
                int lastSpace = chunk.lastIndexOf(' ');
                if (lastSpace > chunk.length() * 0.8) { // Only if we don't lose too much text
                    chunk = chunk.substring(0, lastSpace);
                }
            }
            
            chunks.add(chunk.trim());
        }
        
        return chunks;
    }
    
    private String getOverlapText(String text, int overlapSize) {
        if (text.length() <= overlapSize) {
            return text;
        }
        
        String overlap = text.substring(text.length() - overlapSize);
        
        // Try to start overlap at a sentence boundary
        int sentenceStart = overlap.indexOf(". ");
        if (sentenceStart > 0 && sentenceStart < overlap.length() * 0.5) {
            overlap = overlap.substring(sentenceStart + 2);
        }
        
        // Try to start overlap at a word boundary
        int wordStart = overlap.indexOf(' ');
        if (wordStart > 0 && wordStart < overlap.length() * 0.2) {
            overlap = overlap.substring(wordStart + 1);
        }
        
        return overlap.trim();
    }
    
    public List<String> chunkTextSimple(String text, int chunkSize) {
        return chunkText(text, chunkSize, 0);
    }
    
    public int estimateChunkCount(String text, int chunkSize) {
        if (text == null || text.trim().isEmpty()) {
            return 0;
        }
        return (int) Math.ceil((double) text.length() / chunkSize);
    }
}
