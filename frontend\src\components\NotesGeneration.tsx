'use client';

import React, { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { ProgressSteps } from '@/components/ui/progress-steps';
import {
  Loader2,
  FileText,
  Zap,
  BookOpen,
  PenTool,
  CheckCircle,
  AlertCircle,
  Search,
  ArrowLeft,
  ArrowRight,
  Filter
} from 'lucide-react';
import { notesAPI, documentsAPI } from '@/lib/api';
import { toast } from 'sonner';

interface Document {
  id: number;
  originalFilename: string;
  fileSize: number;
  contentType: string;
  fileType: string;
  processingStatus: 'PENDING' | 'PROCESSING' | 'COMPLETED' | 'FAILED';
  createdAt: string;
  updatedAt: string;
}

interface NotesGenerationProps {
  onNoteGenerated?: (note: any) => void;
}

interface NoteType {
  code: string;
  name: string;
  description: string;
  icon: string;
  category: string;
}

interface GenerationStatus {
  canGenerate: boolean;
  aiAvailable: boolean;
  serviceStatus: string;
  reason?: string;
}

type WorkflowStep = 'document-selection' | 'note-type-selection' | 'generation';

export default function NotesGeneration({
  onNoteGenerated
}: NotesGenerationProps) {
  // Workflow state
  const [currentStep, setCurrentStep] = useState<WorkflowStep>('document-selection');
  const [selectedDocument, setSelectedDocument] = useState<Document | null>(null);
  const [selectedNoteType, setSelectedNoteType] = useState<NoteType | null>(null);

  // Document selection state
  const [documents, setDocuments] = useState<Document[]>([]);
  const [filteredDocuments, setFilteredDocuments] = useState<Document[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [filterType, setFilterType] = useState<string>('all');
  const [loadingDocuments, setLoadingDocuments] = useState(true);

  // Note generation state
  const [noteTypes, setNoteTypes] = useState<NoteType[]>([]);
  const [status, setStatus] = useState<GenerationStatus | null>(null);
  const [generating, setGenerating] = useState<string | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(false);

  useEffect(() => {
    loadDocuments();
    loadNoteTypes();
  }, []);

  // Keyboard navigation
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape') {
        if (currentStep === 'generation') {
          handleBackToNoteTypes();
        } else if (currentStep === 'note-type-selection') {
          handleBackToDocuments();
        }
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [currentStep]);

  useEffect(() => {
    filterDocuments();
  }, [documents, searchQuery, filterType]);

  useEffect(() => {
    if (selectedDocument && currentStep === 'note-type-selection') {
      checkAvailability();
    }
  }, [selectedDocument, currentStep]);

  const loadDocuments = async () => {
    try {
      setLoadingDocuments(true);
      const response = await documentsAPI.getAll();
      const docs = response.data.content || response.data || [];
      setDocuments(docs);
    } catch (error) {
      console.error('Failed to load documents:', error);
      toast.error('Failed to load documents');
    } finally {
      setLoadingDocuments(false);
    }
  };

  const loadNoteTypes = async () => {
    try {
      const response = await notesAPI.getTypes();
      setNoteTypes(response.data.types || []);
    } catch (error) {
      console.error('Failed to load note types:', error);
      toast.error('Failed to load note types');
    }
  };

  const checkAvailability = async () => {
    if (!selectedDocument) return;

    try {
      setLoadingStatus(true);
      const response = await notesAPI.checkAvailability(selectedDocument.id);
      setStatus(response.data);
    } catch (error) {
      console.error('Failed to check availability:', error);
      toast.error('Failed to check notes availability');
    } finally {
      setLoadingStatus(false);
    }
  };

  const filterDocuments = () => {
    let filtered = documents;

    // Filter by search query
    if (searchQuery.trim()) {
      filtered = filtered.filter(doc =>
        doc.originalFilename.toLowerCase().includes(searchQuery.toLowerCase()) ||
        doc.fileType.toLowerCase().includes(searchQuery.toLowerCase())
      );
    }

    // Filter by type
    if (filterType !== 'all') {
      filtered = filtered.filter(doc => doc.fileType === filterType);
    }

    // Only show completed documents
    filtered = filtered.filter(doc => doc.processingStatus === 'COMPLETED');

    setFilteredDocuments(filtered);
  };

  const handleDocumentSelect = (document: Document) => {
    setSelectedDocument(document);
    setCurrentStep('note-type-selection');
  };

  const handleNoteTypeSelect = (noteType: NoteType) => {
    setSelectedNoteType(noteType);
    setCurrentStep('generation');
  };

  const handleBackToDocuments = () => {
    setSelectedDocument(null);
    setSelectedNoteType(null);
    setCurrentStep('document-selection');
  };

  const handleBackToNoteTypes = () => {
    setSelectedNoteType(null);
    setCurrentStep('note-type-selection');
  };

  const generateNotes = async (noteType: string) => {
    if (!selectedDocument || !status?.canGenerate) {
      toast.error('Cannot generate notes for this document');
      return;
    }

    setGenerating(noteType);

    try {
      toast.info('Starting note generation...', { duration: 2000 });

      const response = await notesAPI.generate(selectedDocument.id, noteType);

      if (response.data.success) {
        toast.success('Notes generated successfully!');
        if (onNoteGenerated) {
          onNoteGenerated(response.data.note);
        }
        // Reset to document selection after successful generation
        setTimeout(() => {
          handleBackToDocuments();
        }, 1500); // Give user time to see success message
      } else {
        const errorMessage = response.data.error || 'Failed to generate notes';
        toast.error(errorMessage);
      }
    } catch (error: any) {
      console.error('Failed to generate notes:', error);

      let errorMessage = 'Failed to generate notes';
      if (error.response?.data?.error) {
        errorMessage = error.response.data.error;
      } else if (error.message) {
        errorMessage = `Error: ${error.message}`;
      }

      toast.error(errorMessage, { duration: 5000 });
    } finally {
      setGenerating(null);
    }
  };

  const getUniqueFileTypes = () => {
    const types = [...new Set(documents.map(doc => doc.fileType))];
    return types.sort();
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case '📚': return <BookOpen className="h-6 w-6" />;
      case '⚡': return <Zap className="h-6 w-6" />;
      case '📝': return <PenTool className="h-6 w-6" />;
      default: return <FileText className="h-6 w-6" />;
    }
  };

  const getStatusIcon = () => {
    if (loadingStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (status?.canGenerate && status?.aiAvailable) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (loadingStatus) return 'Checking availability...';
    if (!status?.canGenerate) return status?.reason || 'Document not ready';
    if (!status?.aiAvailable) return 'AI service not available (using fallback)';
    return 'Ready to generate notes';
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case 'basic': return 'bg-blue-100 text-blue-800';
      case 'advanced': return 'bg-purple-100 text-purple-800';
      case 'study aids': return 'bg-green-100 text-green-800';
      case 'visual': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  // Document Selection Step
  const renderDocumentSelection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Select Document for Notes Generation
          </CardTitle>
          <div className="text-sm text-muted-foreground">
            Step 1 of 3: Choose a document to generate notes from
          </div>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search and Filter */}
          <div className="flex gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                placeholder="Search documents by name or type..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>
            <select
              value={filterType}
              onChange={(e) => setFilterType(e.target.value)}
              className="px-3 py-2 border border-input bg-background rounded-md text-sm"
              title="Filter documents by type"
            >
              <option value="all">All Types</option>
              {getUniqueFileTypes().map(type => (
                <option key={type} value={type}>{type.toUpperCase()}</option>
              ))}
            </select>
          </div>

          {/* Documents List */}
          {loadingDocuments ? (
            <div className="flex items-center justify-center py-8">
              <Loader2 className="h-6 w-6 animate-spin" />
              <span className="ml-2">Loading documents...</span>
            </div>
          ) : filteredDocuments.length === 0 ? (
            <div className="text-center py-8 text-muted-foreground">
              <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium mb-2">No Documents Found</h3>
              <p className="text-sm">
                {documents.length === 0
                  ? 'Upload some documents first to generate notes.'
                  : 'No documents match your search criteria.'}
              </p>
            </div>
          ) : (
            <div className="grid gap-3 max-h-96 overflow-y-auto">
              {filteredDocuments.map((document) => (
                <Card
                  key={document.id}
                  className="cursor-pointer transition-all hover:shadow-md hover:border-primary/50"
                  onClick={() => handleDocumentSelect(document)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-3 flex-1 min-w-0">
                        <div className="relative">
                          <FileText className="h-8 w-8 text-primary flex-shrink-0" />
                          <div className="absolute -top-1 -right-1 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                        </div>
                        <div className="flex-1 min-w-0">
                          <h3 className="font-medium truncate">{document.originalFilename}</h3>
                          <div className="flex items-center gap-2 text-sm text-muted-foreground">
                            <Badge variant="outline" className="text-xs">
                              {document.fileType.toUpperCase()}
                            </Badge>
                            <Badge variant="secondary" className="text-xs bg-green-100 text-green-800">
                              Ready
                            </Badge>
                            <span>{formatFileSize(document.fileSize)}</span>
                            <span>•</span>
                            <span>{formatDate(document.createdAt)}</span>
                          </div>
                        </div>
                      </div>
                      <ArrowRight className="h-4 w-4 text-muted-foreground flex-shrink-0" />
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );

  // Note Type Selection Step
  const renderNoteTypeSelection = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToDocuments}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                Choose Note Type for "{selectedDocument?.originalFilename}"
              </CardTitle>
              <div className="text-sm text-muted-foreground mt-1">
                Step 2 of 3: Select the type of notes you want to generate
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-sm mb-4">
            {getStatusIcon()}
            <span>{getStatusText()}</span>
            {status?.aiAvailable && (
              <Badge variant="secondary" className="ml-auto">
                AI Powered
              </Badge>
            )}
          </div>
          {status?.serviceStatus && (
            <div className="text-xs text-muted-foreground mb-4">
              Service: {status.serviceStatus}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Note Types Grid */}
      {loadingStatus ? (
        <Card>
          <CardContent className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Checking document availability...</span>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {noteTypes.map((noteType) => (
            <Card
              key={noteType.code}
              className={`cursor-pointer transition-all hover:shadow-md ${
                !status?.canGenerate ? 'opacity-50 cursor-not-allowed' : 'hover:border-primary/50'
              }`}
              onClick={() => status?.canGenerate && handleNoteTypeSelect(noteType)}
            >
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-3 text-lg">
                  {getIcon(noteType.icon)}
                  <div className="flex-1">
                    <div className="flex items-center justify-between">
                      <span>{noteType.name}</span>
                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    </div>
                    <div className="flex items-center gap-2 mt-1">
                      <Badge variant="outline" className="text-xs font-normal">
                        {noteType.code}
                      </Badge>
                      {noteType.category && (
                        <Badge className={`text-xs ${getCategoryColor(noteType.category)}`}>
                          {noteType.category}
                        </Badge>
                      )}
                    </div>
                  </div>
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  {noteType.description}
                </p>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Status Messages */}
      {!loadingStatus && !status?.canGenerate && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium mb-2">Notes Generation Not Available</h3>
              <p className="text-sm">
                {status?.reason || 'This document is not ready for notes generation.'}
              </p>
            </div>
          </CardContent>
        </Card>
      )}

      {!loadingStatus && status?.canGenerate && !status?.aiAvailable && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="text-center text-yellow-800">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <h3 className="font-medium mb-2">Using Fallback Mode</h3>
              <p className="text-sm">
                AI service is not available. Notes will be generated using basic text processing.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Generation Step
  const renderGeneration = () => (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <div className="flex items-center gap-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleBackToNoteTypes}
              className="mr-2"
            >
              <ArrowLeft className="h-4 w-4" />
            </Button>
            <div className="flex-1">
              <CardTitle className="flex items-center gap-2">
                {selectedNoteType && getIcon(selectedNoteType.icon)}
                Generate {selectedNoteType?.name} Notes
              </CardTitle>
              <div className="text-sm text-muted-foreground mt-1">
                Step 3 of 3: Generating notes from "{selectedDocument?.originalFilename}"
              </div>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* Selected Note Type Details */}
            {selectedNoteType && (
              <div className="p-4 bg-muted rounded-lg">
                <div className="flex items-center gap-3 mb-2">
                  {getIcon(selectedNoteType.icon)}
                  <div>
                    <h3 className="font-medium">{selectedNoteType.name}</h3>
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">
                        {selectedNoteType.code}
                      </Badge>
                      {selectedNoteType.category && (
                        <Badge className={`text-xs ${getCategoryColor(selectedNoteType.category)}`}>
                          {selectedNoteType.category}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  {selectedNoteType.description}
                </p>
              </div>
            )}

            {/* Generation Button */}
            <div className="text-center">
              <Button
                onClick={() => selectedNoteType && generateNotes(selectedNoteType.code)}
                disabled={!status?.canGenerate || generating !== null}
                size="lg"
                className="px-8"
              >
                {generating ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Generating Notes...
                  </>
                ) : (
                  <>
                    {selectedNoteType && getIcon(selectedNoteType.icon)}
                    <span className="ml-2">Generate Notes</span>
                  </>
                )}
              </Button>
            </div>

            {/* Status */}
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              {getStatusIcon()}
              <span>{getStatusText()}</span>
              {status?.aiAvailable && (
                <Badge variant="secondary" className="ml-2">
                  AI Powered
                </Badge>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Progress/Status Messages */}
      {generating && (
        <Card className="border-blue-200 bg-blue-50">
          <CardContent className="pt-6">
            <div className="text-center text-blue-800">
              <div className="relative">
                <Loader2 className="h-12 w-12 mx-auto mb-4 animate-spin" />
                <div className="absolute inset-0 flex items-center justify-center">
                  <div className="w-8 h-8 bg-blue-100 rounded-full animate-pulse"></div>
                </div>
              </div>
              <h3 className="font-medium mb-2">Generating Your Notes</h3>
              <p className="text-sm mb-4">
                Please wait while we process your document and generate {selectedNoteType?.name.toLowerCase()} notes...
              </p>
              <div className="flex items-center justify-center space-x-2 text-xs">
                <div className="flex space-x-1">
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:0.1s]"></div>
                  <div className="w-2 h-2 bg-blue-500 rounded-full animate-bounce [animation-delay:0.2s]"></div>
                </div>
                <span>Processing with AI</span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );

  // Progress steps configuration
  const progressSteps = [
    { id: 'document-selection', title: 'Select Document', description: 'Choose your document' },
    { id: 'note-type-selection', title: 'Choose Type', description: 'Pick note style' },
    { id: 'generation', title: 'Generate', description: 'Create your notes' }
  ];

  const getCompletedSteps = () => {
    const completed = [];
    if (selectedDocument) completed.push('document-selection');
    if (selectedNoteType) completed.push('note-type-selection');
    if (generating) completed.push('generation');
    return completed;
  };

  // Main render function
  return (
    <div className="space-y-8">
      {/* Progress Steps */}
      <ProgressSteps
        steps={progressSteps}
        currentStep={currentStep}
        completedSteps={getCompletedSteps()}
        className="mb-8"
      />

      {/* Step Content */}
      <div className="space-y-6">
        {currentStep === 'document-selection' && renderDocumentSelection()}
        {currentStep === 'note-type-selection' && renderNoteTypeSelection()}
        {currentStep === 'generation' && renderGeneration()}
      </div>
    </div>
  );
}
