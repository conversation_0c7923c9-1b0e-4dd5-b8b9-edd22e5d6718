'use client';

import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  Loader2, 
  FileText, 
  Zap, 
  BookOpen, 
  PenTool,
  CheckCircle,
  AlertCircle
} from 'lucide-react';
import { notesAPI } from '@/lib/api';
import { toast } from 'sonner';

interface NotesGenerationProps {
  documentId: number;
  documentName: string;
  onNoteGenerated?: (note: any) => void;
}

interface NoteType {
  code: string;
  name: string;
  description: string;
  icon: string;
}

interface GenerationStatus {
  canGenerate: boolean;
  aiAvailable: boolean;
  serviceStatus: string;
  reason?: string;
}

export default function NotesGeneration({ 
  documentId, 
  documentName, 
  onNoteGenerated 
}: NotesGenerationProps) {
  const [noteTypes, setNoteTypes] = useState<NoteType[]>([]);
  const [status, setStatus] = useState<GenerationStatus | null>(null);
  const [generating, setGenerating] = useState<string | null>(null);
  const [loadingStatus, setLoadingStatus] = useState(true);

  useEffect(() => {
    loadNoteTypes();
    checkAvailability();
  }, [documentId]);

  const loadNoteTypes = async () => {
    try {
      const response = await notesAPI.getTypes();
      setNoteTypes(response.data.types || []);
    } catch (error) {
      console.error('Failed to load note types:', error);
      toast.error('Failed to load note types');
    }
  };

  const checkAvailability = async () => {
    try {
      setLoadingStatus(true);
      const response = await notesAPI.checkAvailability(documentId);
      setStatus(response.data);
    } catch (error) {
      console.error('Failed to check availability:', error);
      toast.error('Failed to check notes availability');
    } finally {
      setLoadingStatus(false);
    }
  };

  const generateNotes = async (noteType: string) => {
    if (!status?.canGenerate) {
      toast.error('Cannot generate notes for this document');
      return;
    }

    setGenerating(noteType);
    try {
      const response = await notesAPI.generate(documentId, noteType as 'detailed' | 'quick' | 'medium');
      
      if (response.data.success) {
        toast.success('Notes generated successfully!');
        if (onNoteGenerated) {
          onNoteGenerated(response.data.note);
        }
      } else {
        toast.error('Failed to generate notes');
      }
    } catch (error) {
      console.error('Failed to generate notes:', error);
      toast.error('Failed to generate notes');
    } finally {
      setGenerating(null);
    }
  };

  const getIcon = (iconName: string) => {
    switch (iconName) {
      case '📚': return <BookOpen className="h-6 w-6" />;
      case '⚡': return <Zap className="h-6 w-6" />;
      case '📝': return <PenTool className="h-6 w-6" />;
      default: return <FileText className="h-6 w-6" />;
    }
  };

  const getStatusIcon = () => {
    if (loadingStatus) return <Loader2 className="h-4 w-4 animate-spin" />;
    if (status?.canGenerate && status?.aiAvailable) return <CheckCircle className="h-4 w-4 text-green-500" />;
    return <AlertCircle className="h-4 w-4 text-yellow-500" />;
  };

  const getStatusText = () => {
    if (loadingStatus) return 'Checking availability...';
    if (!status?.canGenerate) return status?.reason || 'Document not ready';
    if (!status?.aiAvailable) return 'AI service not available (using fallback)';
    return 'Ready to generate notes';
  };

  if (loadingStatus) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin" />
          <span className="ml-2">Loading notes generation...</span>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Status Card */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <FileText className="h-5 w-5" />
            Generate Notes for {documentName}
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex items-center gap-2 text-sm">
            {getStatusIcon()}
            <span>{getStatusText()}</span>
            {status?.aiAvailable && (
              <Badge variant="secondary" className="ml-auto">
                AI Powered
              </Badge>
            )}
          </div>
          {status?.serviceStatus && (
            <div className="mt-2 text-xs text-muted-foreground">
              Service: {status.serviceStatus}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Note Types */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        {noteTypes.map((noteType) => (
          <Card 
            key={noteType.code}
            className={`cursor-pointer transition-all hover:shadow-md ${
              !status?.canGenerate ? 'opacity-50 cursor-not-allowed' : ''
            }`}
          >
            <CardHeader className="pb-3">
              <CardTitle className="flex items-center gap-3 text-lg">
                {getIcon(noteType.icon)}
                <div>
                  <div>{noteType.name}</div>
                  <Badge variant="outline" className="text-xs font-normal mt-1">
                    {noteType.code}
                  </Badge>
                </div>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-sm text-muted-foreground">
                {noteType.description}
              </p>
              
              <Button
                onClick={() => generateNotes(noteType.code)}
                disabled={!status?.canGenerate || generating === noteType.code}
                className="w-full"
                variant={noteType.code === 'detailed' ? 'default' : 'outline'}
              >
                {generating === noteType.code ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    Generating...
                  </>
                ) : (
                  <>
                    {getIcon(noteType.icon)}
                    <span className="ml-2">Generate {noteType.name}</span>
                  </>
                )}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Help Text */}
      {!status?.canGenerate && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <AlertCircle className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium mb-2">Notes Generation Not Available</h3>
              <p className="text-sm">
                {status?.reason || 'This document is not ready for notes generation.'}
              </p>
              <div className="mt-4 text-xs">
                <p>Make sure the document has been fully processed before generating notes.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {status?.canGenerate && !status?.aiAvailable && (
        <Card className="border-yellow-200 bg-yellow-50">
          <CardContent className="pt-6">
            <div className="text-center text-yellow-800">
              <AlertCircle className="h-8 w-8 mx-auto mb-2" />
              <h3 className="font-medium mb-2">Using Fallback Mode</h3>
              <p className="text-sm">
                AI service is not available. Notes will be generated using basic text processing.
              </p>
              <div className="mt-2 text-xs">
                <p>For AI-powered notes, configure your Groq API key.</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {status?.canGenerate && status?.aiAvailable && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="text-center text-green-800">
              <CheckCircle className="h-8 w-8 mx-auto mb-2" />
              <h3 className="font-medium mb-2">AI Notes Ready</h3>
              <p className="text-sm">
                Choose a note type above to generate AI-powered notes from your document.
              </p>
              <div className="mt-4 text-xs space-y-1">
                <p><strong>Detailed:</strong> Comprehensive notes with all important details</p>
                <p><strong>Quick:</strong> Concise notes for quick review and revision</p>
                <p><strong>Medium:</strong> Balanced notes with key concepts and details</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
