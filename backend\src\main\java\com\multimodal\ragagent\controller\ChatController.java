package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.ChatMessage;
import com.multimodal.ragagent.entity.ChatSession;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.ChatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@RestController
@RequestMapping("/api/chat")
public class ChatController {
    
    @Autowired
    private ChatService chatService;
    
    @PostMapping("/sessions")
    public ResponseEntity<?> createChatSession(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            Long documentId = Long.valueOf(request.get("documentId").toString());
            
            if (!chatService.canCreateChatSession(documentId, user)) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Cannot create chat session. Document may not be processed yet or you don't have access.");
                return ResponseEntity.badRequest().body(error);
            }
            
            ChatSession chatSession = chatService.createChatSession(documentId, user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("session", createSessionResponse(chatSession));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to create chat session: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PostMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<?> sendMessage(
            @PathVariable Long sessionId,
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            String message = request.get("message").toString();
            
            if (message == null || message.trim().isEmpty()) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Message cannot be empty");
                return ResponseEntity.badRequest().body(error);
            }
            
            ChatMessage aiResponse = chatService.sendMessage(sessionId, message.trim(), user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", createMessageResponse(aiResponse));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to send message: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/sessions")
    public ResponseEntity<?> getUserChatSessions(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @AuthenticationPrincipal User user) {
        try {
            Pageable pageable = PageRequest.of(page, size);
            Page<ChatSession> sessionsPage = chatService.getUserChatSessions(user, pageable);
            
            Map<String, Object> response = new HashMap<>();
            response.put("sessions", sessionsPage.getContent().stream()
                    .map(this::createSessionResponse)
                    .collect(Collectors.toList()));
            response.put("totalElements", sessionsPage.getTotalElements());
            response.put("totalPages", sessionsPage.getTotalPages());
            response.put("currentPage", page);
            response.put("pageSize", size);
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get chat sessions: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/sessions/{sessionId}")
    public ResponseEntity<?> getChatSession(
            @PathVariable Long sessionId,
            @AuthenticationPrincipal User user) {
        try {
            ChatSession chatSession = chatService.getChatSession(sessionId, user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("session", createSessionResponse(chatSession));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get chat session: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/sessions/{sessionId}/messages")
    public ResponseEntity<?> getChatMessages(
            @PathVariable Long sessionId,
            @AuthenticationPrincipal User user) {
        try {
            List<ChatMessage> messages = chatService.getChatMessages(sessionId, user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("messages", messages.stream()
                    .map(this::createMessageResponse)
                    .collect(Collectors.toList()));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get chat messages: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/documents/{documentId}/sessions")
    public ResponseEntity<?> getDocumentChatSessions(
            @PathVariable Long documentId,
            @AuthenticationPrincipal User user) {
        try {
            List<ChatSession> sessions = chatService.getDocumentChatSessions(documentId, user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("sessions", sessions.stream()
                    .map(this::createSessionResponse)
                    .collect(Collectors.toList()));
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get document chat sessions: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PutMapping("/sessions/{sessionId}/title")
    public ResponseEntity<?> updateSessionTitle(
            @PathVariable Long sessionId,
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            String newTitle = request.get("title").toString();
            
            if (newTitle == null || newTitle.trim().isEmpty()) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "Title cannot be empty");
                return ResponseEntity.badRequest().body(error);
            }
            
            chatService.updateSessionTitle(sessionId, newTitle.trim(), user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Session title updated successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to update session title: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @DeleteMapping("/sessions/{sessionId}")
    public ResponseEntity<?> deleteChatSession(
            @PathVariable Long sessionId,
            @AuthenticationPrincipal User user) {
        try {
            chatService.deleteChatSession(sessionId, user);
            
            Map<String, Object> response = new HashMap<>();
            response.put("success", true);
            response.put("message", "Chat session deleted successfully");
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to delete chat session: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/statistics")
    public ResponseEntity<?> getChatStatistics(@AuthenticationPrincipal User user) {
        try {
            Map<String, Object> statistics = chatService.getChatStatistics(user);
            return ResponseEntity.ok(statistics);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to get chat statistics: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    private Map<String, Object> createSessionResponse(ChatSession session) {
        Map<String, Object> sessionData = new HashMap<>();
        sessionData.put("id", session.getId());
        sessionData.put("title", session.getTitle());
        sessionData.put("documentId", session.getDocument().getId());
        sessionData.put("documentName", session.getDocument().getOriginalFilename());
        sessionData.put("createdAt", session.getCreatedAt().toString());
        sessionData.put("updatedAt", session.getUpdatedAt().toString());
        sessionData.put("messageCount", session.getMessageCount());
        
        return sessionData;
    }
    
    private Map<String, Object> createMessageResponse(ChatMessage message) {
        Map<String, Object> messageData = new HashMap<>();
        messageData.put("id", message.getId());
        messageData.put("role", message.getRole().name().toLowerCase());
        messageData.put("content", message.getContent());
        messageData.put("createdAt", message.getCreatedAt().toString());
        
        if (message.getResponseTimeMs() != null) {
            messageData.put("responseTimeMs", message.getResponseTimeMs());
        }
        
        return messageData;
    }
}
