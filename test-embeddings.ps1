Write-Host "==================================="
Write-Host "   VECTOR DATABASE EMBEDDING TEST"
Write-Host "==================================="
Write-Host ""

# Test 1: Check ChromaDB Collections
Write-Host "🔍 Step 1: Checking ChromaDB Collections..."
try {
    $response = Invoke-WebRequest -Uri "http://localhost:8000/api/v1/collections" -Method GET -UseBasicParsing
    if ($response.StatusCode -eq 400) {
        Write-Host "✅ ChromaDB is running (400 = no collections yet)" -ForegroundColor Green
        Write-Host "   This means no documents have been uploaded and embedded yet."
    } else {
        Write-Host "✅ ChromaDB Status: $($response.StatusCode)" -ForegroundColor Green
        Write-Host "   Response: $($response.Content)"
    }
} catch {
    Write-Host "❌ ChromaDB Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 2: Check Backend Status
Write-Host "🔍 Step 2: Checking Backend RAG Status..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/rag/status" -Method GET
    Write-Host "✅ Backend Status: $($response.status)" -ForegroundColor Green
    Write-Host "   Message: $($response.message)"
    Write-Host "   Vector DB: $($response.vectorDatabase)"
    Write-Host "   Embedding: $($response.embedding)"
} catch {
    Write-Host "❌ Backend Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""

# Test 3: Check Documents in Database
Write-Host "🔍 Step 3: Checking Uploaded Documents..."
try {
    $response = Invoke-RestMethod -Uri "http://localhost:8081/api/documents?page=0`&size=10" -Method GET
    if ($response.content -and $response.content.Count -gt 0) {
        Write-Host "✅ Found $($response.content.Count) document(s):" -ForegroundColor Green
        foreach ($doc in $response.content) {
            Write-Host "   📄 $($doc.filename) (ID: $($doc.id))"
            Write-Host "      Status: $($doc.status)"
            Write-Host "      Uploaded: $($doc.uploadedAt)"
        }
    } else {
        Write-Host "⚠️  No documents found in database" -ForegroundColor Yellow
        Write-Host "   Upload a document to test vector embeddings"
    }
} catch {
    Write-Host "❌ Documents Error: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "==================================="
Write-Host "📋 SUMMARY:"
Write-Host "1. ChromaDB: Running and ready"
Write-Host "2. Backend: Vector RAG system active"
Write-Host "3. Embeddings: Will be created when you upload documents"
Write-Host ""
Write-Host "🎯 TO SEE EMBEDDINGS IN ACTION:"
Write-Host "1. Go to http://localhost:3000"
Write-Host "2. Login (admin/admin)"
Write-Host "3. Upload a document in Documents tab"
Write-Host "4. Watch the backend logs for embedding generation"
Write-Host "5. Run this script again to see stored vectors"
Write-Host "==================================="
