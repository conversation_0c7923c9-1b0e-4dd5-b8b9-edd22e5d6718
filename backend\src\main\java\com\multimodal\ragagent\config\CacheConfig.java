package com.multimodal.ragagent.config;

import com.github.benmanes.caffeine.cache.Caffeine;
import org.springframework.cache.CacheManager;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.caffeine.CaffeineCacheManager;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

import java.util.concurrent.TimeUnit;

@Configuration
@EnableCaching
public class CacheConfig {

    @Bean
    public CacheManager cacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Configure different cache settings for different use cases
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .initialCapacity(100)
            .maximumSize(1000)
            .expireAfterAccess(30, TimeUnit.MINUTES)
            .expireAfterWrite(60, TimeUnit.MINUTES)
            .recordStats());
            
        // Register cache names
        cacheManager.setCacheNames(
            "documents",
            "notes", 
            "chatSessions",
            "noteTypes",
            "userDocuments",
            "documentChunks",
            "vectorSearchResults",
            "ragStatus"
        );
        
        return cacheManager;
    }

    @Bean("longTermCache")
    public CacheManager longTermCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Long-term cache for static data
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .initialCapacity(50)
            .maximumSize(500)
            .expireAfterWrite(24, TimeUnit.HOURS)
            .recordStats());
            
        cacheManager.setCacheNames(
            "systemStatus",
            "aiModels",
            "templates"
        );
        
        return cacheManager;
    }

    @Bean("shortTermCache")
    public CacheManager shortTermCacheManager() {
        CaffeineCacheManager cacheManager = new CaffeineCacheManager();
        
        // Short-term cache for frequently changing data
        cacheManager.setCaffeine(Caffeine.newBuilder()
            .initialCapacity(200)
            .maximumSize(2000)
            .expireAfterAccess(5, TimeUnit.MINUTES)
            .expireAfterWrite(10, TimeUnit.MINUTES)
            .recordStats());
            
        cacheManager.setCacheNames(
            "recentMessages",
            "activeChats",
            "processingStatus"
        );
        
        return cacheManager;
    }
}
