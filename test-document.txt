This is a test document for the multimodal RAG agent.

The document contains multiple paragraphs to test the text chunking functionality.

Here are some key concepts about artificial intelligence:

1. Machine Learning: A subset of AI that enables computers to learn and improve from experience without being explicitly programmed.

2. Natural Language Processing: The ability of computers to understand, interpret, and generate human language.

3. Computer Vision: The field of AI that enables computers to interpret and understand visual information from the world.

4. Deep Learning: A subset of machine learning that uses neural networks with multiple layers to model and understand complex patterns.

This document will be processed by our RAG system to extract text, create chunks, and enable question-answering capabilities.

The system should be able to answer questions about the content above, such as:
- What is machine learning?
- What are the main areas of AI mentioned?
- How does the RAG system work?

This is the end of the test document.
