package com.multimodal.ragagent.repository;

import com.multimodal.ragagent.entity.ChatMessage;
import com.multimodal.ragagent.entity.ChatSession;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatMessageRepository extends JpaRepository<ChatMessage, Long> {
    
    List<ChatMessage> findByChatSessionOrderByCreatedAtAsc(ChatSession chatSession);
    
    Page<ChatMessage> findByChatSessionOrderByCreatedAtAsc(ChatSession chatSession, Pageable pageable);
    
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.chatSession = :chatSession ORDER BY cm.createdAt ASC")
    List<ChatMessage> findMessagesBySession(@Param("chatSession") ChatSession chatSession);
    
    @Query("SELECT cm FROM ChatMessage cm WHERE cm.chatSession = :chatSession AND cm.role = :role ORDER BY cm.createdAt ASC")
    List<ChatMessage> findMessagesBySessionAndRole(@Param("chatSession") ChatSession chatSession, 
                                                   @Param("role") ChatMessage.MessageRole role);
    
    @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.chatSession = :chatSession")
    long countMessagesBySession(@Param("chatSession") ChatSession chatSession);
    
    @Query("SELECT COUNT(cm) FROM ChatMessage cm WHERE cm.chatSession = :chatSession AND cm.role = :role")
    long countMessagesBySessionAndRole(@Param("chatSession") ChatSession chatSession, 
                                      @Param("role") ChatMessage.MessageRole role);
    
    void deleteByChatSession(ChatSession chatSession);
}
