package com.multimodal.ragagent.entity;

import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "document_chunks", indexes = {
    @Index(name = "idx_chunk_document_id", columnList = "document_id"),
    @Index(name = "idx_chunk_vector_id", columnList = "vector_id"),
    @Index(name = "idx_chunk_type", columnList = "chunk_type"),
    @Index(name = "idx_chunk_index", columnList = "chunk_index"),
    @Index(name = "idx_chunk_document_index", columnList = "document_id, chunk_index")
})
public class DocumentChunk {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    @Column(name = "content", columnDefinition = "TEXT")
    private String content;
    
    @NotNull
    @Column(name = "chunk_index")
    private Integer chunkIndex;
    
    @NotNull
    @Column(name = "start_position")
    private Integer startPosition;
    
    @NotNull
    @Column(name = "end_position")
    private Integer endPosition;
    
    @Column(name = "vector_id")
    private String vectorId;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "chunk_type")
    private ChunkType chunkType = ChunkType.TEXT;
    
    @Column(name = "metadata", columnDefinition = "TEXT")
    private String metadata;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;
    
    public enum ChunkType {
        TEXT, IMAGE, TABLE, HANDWRITTEN
    }
    
    // Constructors
    public DocumentChunk() {}
    
    public DocumentChunk(String content, Integer chunkIndex, Integer startPosition, 
                        Integer endPosition, Document document) {
        this.content = content;
        this.chunkIndex = chunkIndex;
        this.startPosition = startPosition;
        this.endPosition = endPosition;
        this.document = document;
        this.createdAt = LocalDateTime.now();
    }
    
    // Lifecycle callbacks
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public Integer getChunkIndex() { return chunkIndex; }
    public void setChunkIndex(Integer chunkIndex) { this.chunkIndex = chunkIndex; }
    
    public Integer getStartPosition() { return startPosition; }
    public void setStartPosition(Integer startPosition) { this.startPosition = startPosition; }
    
    public Integer getEndPosition() { return endPosition; }
    public void setEndPosition(Integer endPosition) { this.endPosition = endPosition; }
    
    public String getVectorId() { return vectorId; }
    public void setVectorId(String vectorId) { this.vectorId = vectorId; }
    
    public ChunkType getChunkType() { return chunkType; }
    public void setChunkType(ChunkType chunkType) { this.chunkType = chunkType; }
    
    public String getMetadata() { return metadata; }
    public void setMetadata(String metadata) { this.metadata = metadata; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public Document getDocument() { return document; }
    public void setDocument(Document document) { this.document = document; }
}
