version: '3.8'

services:
  # PostgreSQL Database for Development
  postgres-dev:
    image: postgres:15-alpine
    container_name: multimodal-rag-postgres-dev
    environment:
      POSTGRES_DB: multimodal_rag_dev
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: password
    ports:
      - "5433:5432"
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
    networks:
      - rag-dev-network

  # Chroma Vector Database for Development
  chroma-dev:
    image: chromadb/chroma:latest
    container_name: multimodal-rag-chroma-dev
    ports:
      - "8001:8000"
    volumes:
      - chroma_dev_data:/chroma/chroma
    environment:
      - CHROMA_SERVER_HOST=0.0.0.0
      - CHROMA_SERVER_HTTP_PORT=8000
    networks:
      - rag-dev-network

  # Redis for caching (optional)
  redis-dev:
    image: redis:7-alpine
    container_name: multimodal-rag-redis-dev
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    networks:
      - rag-dev-network

volumes:
  postgres_dev_data:
  chroma_dev_data:
  redis_dev_data:

networks:
  rag-dev-network:
    driver: bridge
