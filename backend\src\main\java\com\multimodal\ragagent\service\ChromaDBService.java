package com.multimodal.ragagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import org.apache.hc.client5.http.classic.methods.HttpDelete;
import org.apache.hc.client5.http.classic.methods.HttpGet;
import org.apache.hc.client5.http.classic.methods.HttpPost;
import org.apache.hc.client5.http.impl.classic.CloseableHttpClient;
import org.apache.hc.client5.http.impl.classic.HttpClients;
import org.apache.hc.core5.http.ContentType;
import org.apache.hc.core5.http.io.entity.StringEntity;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.Map;

@Service
public class ChromaDBService {
    
    private static final Logger logger = LoggerFactory.getLogger(ChromaDBService.class);
    
    @Value("${vector.db.host:localhost}")
    private String chromaHost;
    
    @Value("${vector.db.port:8000}")
    private int chromaPort;
    
    @Value("${vector.chroma.collection-name:document_embeddings}")
    private String collectionName;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final CloseableHttpClient httpClient = HttpClients.createDefault();
    
    private String getBaseUrl() {
        return String.format("http://%s:%d/api/v1", chromaHost, chromaPort);
    }
    
    public void createCollection() throws IOException {
        String url = getBaseUrl() + "/collections";
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        requestBody.put("name", collectionName);
        requestBody.put("metadata", objectMapper.createObjectNode());
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        try {
            httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                if (statusCode == 200 || statusCode == 409) { // 409 = already exists
                    logger.info("Collection '{}' is ready", collectionName);
                } else {
                    logger.error("Failed to create collection. Status: {}", statusCode);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("Error creating collection: {}", e.getMessage());
            throw new IOException("Failed to create ChromaDB collection", e);
        }
    }
    
    public void addEmbeddings(List<String> ids, List<List<Double>> embeddings, 
                             List<String> documents, List<Map<String, Object>> metadatas) throws IOException {
        String url = getBaseUrl() + "/collections/" + collectionName + "/add";
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        
        // Convert lists to JSON arrays
        ArrayNode idsArray = objectMapper.createArrayNode();
        ids.forEach(idsArray::add);
        
        ArrayNode embeddingsArray = objectMapper.createArrayNode();
        embeddings.forEach(embedding -> {
            ArrayNode embeddingArray = objectMapper.createArrayNode();
            embedding.forEach(embeddingArray::add);
            embeddingsArray.add(embeddingArray);
        });
        
        ArrayNode documentsArray = objectMapper.createArrayNode();
        documents.forEach(documentsArray::add);
        
        ArrayNode metadatasArray = objectMapper.createArrayNode();
        metadatas.forEach(metadata -> {
            ObjectNode metadataNode = objectMapper.createObjectNode();
            metadata.forEach((key, value) -> {
                if (value instanceof String) {
                    metadataNode.put(key, (String) value);
                } else if (value instanceof Number) {
                    metadataNode.put(key, ((Number) value).doubleValue());
                } else if (value instanceof Boolean) {
                    metadataNode.put(key, (Boolean) value);
                }
            });
            metadatasArray.add(metadataNode);
        });
        
        requestBody.set("ids", idsArray);
        requestBody.set("embeddings", embeddingsArray);
        requestBody.set("documents", documentsArray);
        requestBody.set("metadatas", metadatasArray);
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        try {
            httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                if (statusCode == 200) {
                    logger.info("Successfully added {} embeddings to collection", ids.size());
                } else {
                    logger.error("Failed to add embeddings. Status: {}", statusCode);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("Error adding embeddings: {}", e.getMessage());
            throw new IOException("Failed to add embeddings to ChromaDB", e);
        }
    }
    
    public QueryResult queryEmbeddings(List<Double> queryEmbedding, int nResults) throws IOException {
        String url = getBaseUrl() + "/collections/" + collectionName + "/query";
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        
        ArrayNode queryEmbeddingsArray = objectMapper.createArrayNode();
        ArrayNode singleQueryArray = objectMapper.createArrayNode();
        queryEmbedding.forEach(singleQueryArray::add);
        queryEmbeddingsArray.add(singleQueryArray);
        
        requestBody.set("query_embeddings", queryEmbeddingsArray);
        requestBody.put("n_results", nResults);
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        try {
            return httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                if (statusCode == 200) {
                    String responseBody = new String(response.getEntity().getContent().readAllBytes());
                    JsonNode jsonResponse = objectMapper.readTree(responseBody);
                    return parseQueryResult(jsonResponse);
                } else {
                    logger.error("Failed to query embeddings. Status: {}", statusCode);
                    throw new IOException("Query failed with status: " + statusCode);
                }
            });
        } catch (Exception e) {
            logger.error("Error querying embeddings: {}", e.getMessage());
            throw new IOException("Failed to query ChromaDB", e);
        }
    }
    
    public void deleteEmbeddings(List<String> ids) throws IOException {
        String url = getBaseUrl() + "/collections/" + collectionName + "/delete";
        
        ObjectNode requestBody = objectMapper.createObjectNode();
        ArrayNode idsArray = objectMapper.createArrayNode();
        ids.forEach(idsArray::add);
        requestBody.set("ids", idsArray);
        
        HttpPost request = new HttpPost(url);
        request.setEntity(new StringEntity(requestBody.toString(), ContentType.APPLICATION_JSON));
        
        try {
            httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                if (statusCode == 200) {
                    logger.info("Successfully deleted {} embeddings", ids.size());
                } else {
                    logger.error("Failed to delete embeddings. Status: {}", statusCode);
                }
                return null;
            });
        } catch (Exception e) {
            logger.error("Error deleting embeddings: {}", e.getMessage());
            throw new IOException("Failed to delete embeddings from ChromaDB", e);
        }
    }
    
    private QueryResult parseQueryResult(JsonNode jsonResponse) {
        QueryResult result = new QueryResult();
        
        JsonNode ids = jsonResponse.get("ids").get(0);
        JsonNode documents = jsonResponse.get("documents").get(0);
        JsonNode distances = jsonResponse.get("distances").get(0);
        JsonNode metadatas = jsonResponse.get("metadatas").get(0);
        
        for (int i = 0; i < ids.size(); i++) {
            QueryResult.ResultItem item = new QueryResult.ResultItem();
            item.setId(ids.get(i).asText());
            item.setDocument(documents.get(i).asText());
            item.setDistance(distances.get(i).asDouble());
            
            JsonNode metadata = metadatas.get(i);
            if (metadata != null && !metadata.isNull()) {
                item.setDocumentId(metadata.get("document_id").asLong());
                item.setChunkIndex(metadata.get("chunk_index").asInt());
                item.setStartPosition(metadata.get("start_position").asInt());
                item.setEndPosition(metadata.get("end_position").asInt());
            }
            
            result.getResults().add(item);
        }
        
        return result;
    }
    
    public boolean isHealthy() {
        try {
            // Try to get collections list as a health check
            String url = getBaseUrl() + "/collections";
            HttpGet request = new HttpGet(url);

            return httpClient.execute(request, response -> {
                int statusCode = response.getCode();
                logger.debug("ChromaDB health check response: {}", statusCode);
                return statusCode == 200;
            });
        } catch (Exception e) {
            logger.debug("ChromaDB health check failed: {}", e.getMessage());
            return false;
        }
    }
}
