Write-Host "Testing Hugging Face API with simple request..."

$apiKey = "*************************************"
$model = "sentence-transformers/all-MiniLM-L6-v2"

$headers = @{
    'Authorization' = "Bearer $apiKey"
    'Content-Type' = 'application/json'
}

# Simple request format
$body = @{
    inputs = "This is a test sentence"
} | ConvertTo-Json

$endpoint = "https://api-inference.huggingface.co/models/$model"

Write-Host "Testing endpoint: $endpoint" -ForegroundColor Cyan
Write-Host "Request body: $body" -ForegroundColor Yellow

try {
    $response = Invoke-RestMethod -Uri $endpoint -Method POST -Headers $headers -Body $body -TimeoutSec 30
    Write-Host "SUCCESS!" -ForegroundColor Green
    Write-Host "Response type:" $response.GetType().Name
    
    if ($response -is [array]) {
        Write-Host "Embedding dimensions:" $response.Length
        Write-Host "First 5 values:" ($response[0..4] -join ", ")
    }
    
} catch {
    Write-Host "ERROR:" -ForegroundColor Red
    Write-Host $_.Exception.Message
    
    if ($_.Exception.Response) {
        Write-Host "Status Code:" $_.Exception.Response.StatusCode
        
        try {
            $errorStream = $_.Exception.Response.GetResponseStream()
            $reader = New-Object System.IO.StreamReader($errorStream)
            $errorBody = $reader.ReadToEnd()
            Write-Host "Error Body:" $errorBody
        } catch {
            Write-Host "Could not read error body"
        }
    }
}
