package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.service.HuggingFaceEmbeddingService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/test")
public class TestController {
    
    private static final Logger logger = LoggerFactory.getLogger(TestController.class);
    
    @Autowired
    private HuggingFaceEmbeddingService huggingFaceEmbeddingService;
    
    @PostMapping("/embedding")
    public ResponseEntity<Map<String, Object>> testEmbedding(@RequestBody Map<String, String> request) {
        try {
            String text = request.getOrDefault("text", "This is a test sentence for embedding generation.");
            
            logger.info("Testing Hugging Face embedding for text: {}", text);
            
            Map<String, Object> response = new HashMap<>();
            
            if (!huggingFaceEmbeddingService.isConfigured()) {
                response.put("success", false);
                response.put("error", "Hugging Face API key not configured");
                return ResponseEntity.badRequest().body(response);
            }
            
            List<Double> embedding = huggingFaceEmbeddingService.generateEmbedding(text);
            
            response.put("success", true);
            response.put("text", text);
            response.put("embeddingDimensions", embedding.size());
            response.put("embeddingPreview", embedding.subList(0, Math.min(10, embedding.size())));
            response.put("model", huggingFaceEmbeddingService.getEmbeddingModel());
            response.put("configured", huggingFaceEmbeddingService.isConfigured());
            
            logger.info("Successfully generated embedding with {} dimensions", embedding.size());
            
            return ResponseEntity.ok(response);
            
        } catch (Exception e) {
            logger.error("Failed to test embedding: {}", e.getMessage());
            Map<String, Object> errorResponse = new HashMap<>();
            errorResponse.put("success", false);
            errorResponse.put("error", "Failed to generate embedding: " + e.getMessage());
            return ResponseEntity.internalServerError().body(errorResponse);
        }
    }
    
    @GetMapping("/embedding-status")
    public ResponseEntity<Map<String, Object>> getEmbeddingStatus() {
        Map<String, Object> response = new HashMap<>();
        
        response.put("configured", huggingFaceEmbeddingService.isConfigured());
        response.put("model", huggingFaceEmbeddingService.getEmbeddingModel());
        response.put("connectionTest", huggingFaceEmbeddingService.testConnection());
        
        return ResponseEntity.ok(response);
    }
}
