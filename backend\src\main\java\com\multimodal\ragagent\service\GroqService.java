package com.multimodal.ragagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class GroqService {
    
    private static final Logger logger = LoggerFactory.getLogger(GroqService.class);
    
    @Value("${groq.api.key:}")
    private String groqApiKey;
    
    @Value("${groq.api.base-url:https://api.groq.com/openai/v1}")
    private String baseUrl;
    
    @Value("${groq.model.default:llama3-8b-8192}")
    private String defaultModel;
    
    @Value("${groq.model.fast:llama3-8b-8192}")
    private String fastModel;
    
    @Value("${groq.model.quality:llama3-70b-8192}")
    private String qualityModel;
    
    @Value("${groq.settings.max-tokens:2048}")
    private int maxTokens;
    
    @Value("${groq.settings.temperature:0.7}")
    private double temperature;
    
    @Value("${groq.settings.timeout-seconds:30}")
    private int timeoutSeconds;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private OkHttpClient httpClient;
    
    private OkHttpClient getHttpClient() {
        if (httpClient == null) {
            httpClient = new OkHttpClient.Builder()
                    .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .build();
        }
        return httpClient;
    }
    
    public String generateResponse(String prompt, String context) {
        return generateResponse(prompt, context, defaultModel);
    }
    
    public String generateResponse(String prompt, String context, String model) {
        try {
            if (!isConfigured()) {
                return generateFallbackResponse(prompt, context);
            }
            
            String systemPrompt = buildSystemPrompt();
            String userPrompt = buildUserPrompt(prompt, context);
            
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", model);
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);
            
            ArrayNode messages = objectMapper.createArrayNode();
            
            // System message
            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
            
            // User message
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", userPrompt);
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );
            
            Request request = new Request.Builder()
                    .url(baseUrl + "/chat/completions")
                    .addHeader("Authorization", "Bearer " + groqApiKey)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            
            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API request failed with status: {}", response.code());
                    return generateFallbackResponse(prompt, context);
                }
                
                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                JsonNode choices = jsonResponse.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode message = firstChoice.get("message");
                    if (message != null) {
                        String content = message.get("content").asText();
                        logger.info("Generated response using Groq model: {}", model);
                        return content;
                    }
                }
                
                logger.warn("Unexpected response format from Groq API");
                return generateFallbackResponse(prompt, context);
            }
            
        } catch (Exception e) {
            logger.error("Error calling Groq API: {}", e.getMessage());
            return generateFallbackResponse(prompt, context);
        }
    }
    
    public String generateRAGResponse(String question, String context, String documentName) {
        String prompt = String.format(
            "Based on the provided context from the document '%s', please answer the following question: %s",
            documentName != null ? documentName : "the uploaded document",
            question
        );

        return generateResponse(prompt, context, defaultModel);
    }

    public String generateDocumentResponse(String question, String documentContent, String documentName) {
        try {
            if (!isConfigured()) {
                return generateFallbackDocumentResponse(question, documentContent, documentName);
            }

            // Create a comprehensive prompt for document analysis
            String systemPrompt = "You are an expert document analyst. Analyze the provided document content and answer questions accurately and comprehensively. " +
                                "Provide detailed, informative responses based on the document content. " +
                                "If the question asks for a summary, provide a well-structured summary. " +
                                "Always base your response on the actual document content provided.";

            String userPrompt = String.format(
                "Document: %s\n\nContent:\n%s\n\nQuestion: %s\n\nPlease provide a comprehensive answer based on the document content.",
                documentName != null ? documentName : "Uploaded Document",
                documentContent.length() > 8000 ? documentContent.substring(0, 8000) + "..." : documentContent,
                question
            );

            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", qualityModel); // Use quality model for document analysis
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);

            ArrayNode messages = objectMapper.createArrayNode();

            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);

            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", userPrompt);
            messages.add(userMessage);

            requestBody.set("messages", messages);

            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );

            Request request = new Request.Builder()
                .url(baseUrl + "/chat/completions")
                .header("Authorization", "Bearer " + groqApiKey)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API request failed: {}", response.code());
                    return generateFallbackDocumentResponse(question, documentContent, documentName);
                }

                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);

                if (jsonResponse.has("choices") && jsonResponse.get("choices").size() > 0) {
                    return jsonResponse.get("choices").get(0)
                        .get("message").get("content").asText();
                } else {
                    logger.error("Unexpected Groq API response format");
                    return generateFallbackDocumentResponse(question, documentContent, documentName);
                }
            }

        } catch (Exception e) {
            logger.error("Error calling Groq API for document analysis: {}", e.getMessage());
            return generateFallbackDocumentResponse(question, documentContent, documentName);
        }
    }

    private String generateFallbackDocumentResponse(String question, String documentContent, String documentName) {
        // Intelligent fallback response when Groq is not available
        return String.format(
            "Based on the document '%s', I can see the content contains information that may be relevant to your question: '%s'. " +
            "However, to provide you with the most accurate and detailed response, please configure the Groq API key. " +
            "In the meantime, you can review the document content directly for the information you're looking for.",
            documentName != null ? documentName : "the uploaded document",
            question
        );
    }

    public String generateChatGPTLevelResponse(String question, String documentContent, String documentName) {
        try {
            if (!isConfigured()) {
                return generateIntelligentFallback(question, documentContent, documentName);
            }

            // Create a comprehensive system prompt for ChatGPT-4 level responses
            String systemPrompt = "You are an expert AI assistant with advanced document analysis capabilities, similar to ChatGPT-4. " +
                                "You excel at understanding complex documents, extracting key information, and providing comprehensive, well-structured responses. " +
                                "Your responses should be:\n" +
                                "- Detailed and informative\n" +
                                "- Well-structured with clear sections\n" +
                                "- Professional yet conversational\n" +
                                "- Based entirely on the provided document content\n" +
                                "- Include specific examples and quotes when relevant\n" +
                                "- Provide context and explanations\n" +
                                "- Use formatting (bullet points, sections) when appropriate\n\n" +
                                "Always analyze the document thoroughly and provide insights that demonstrate deep understanding.";

            String userPrompt = String.format(
                "Document: %s\n\n" +
                "Full Document Content:\n%s\n\n" +
                "User Question: %s\n\n" +
                "Please provide a comprehensive, ChatGPT-4 level response based on the document content. " +
                "Analyze the document thoroughly and provide detailed insights, explanations, and specific information that directly addresses the user's question.",
                documentName != null ? documentName : "Document",
                documentContent.length() > 12000 ? documentContent.substring(0, 12000) + "\n\n[Document continues...]" : documentContent,
                question
            );

            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", qualityModel); // Use the highest quality model
            requestBody.put("max_tokens", 4096); // Increased for comprehensive responses
            requestBody.put("temperature", 0.3); // Lower temperature for more focused responses

            ArrayNode messages = objectMapper.createArrayNode();

            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);

            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", userPrompt);
            messages.add(userMessage);

            requestBody.set("messages", messages);

            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );

            Request request = new Request.Builder()
                .url(baseUrl + "/chat/completions")
                .header("Authorization", "Bearer " + groqApiKey)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API request failed with status: {}", response.code());
                    return generateIntelligentFallback(question, documentContent, documentName);
                }

                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);

                if (jsonResponse.has("choices") && jsonResponse.get("choices").size() > 0) {
                    String aiResponse = jsonResponse.get("choices").get(0)
                        .get("message").get("content").asText();

                    logger.info("Generated ChatGPT-4 level response for document: {}", documentName);
                    return aiResponse;
                } else {
                    logger.error("Unexpected Groq API response format");
                    return generateIntelligentFallback(question, documentContent, documentName);
                }
            }

        } catch (Exception e) {
            logger.error("Error generating ChatGPT-4 level response: {}", e.getMessage());
            return generateIntelligentFallback(question, documentContent, documentName);
        }
    }

    private String generateIntelligentFallback(String question, String documentContent, String documentName) {
        // Provide a more intelligent fallback when Groq is not available
        String questionLower = question.toLowerCase();

        if (questionLower.contains("summary") || questionLower.contains("summarize") || questionLower.contains("overview")) {
            return generateDocumentSummaryFallback(documentContent, documentName);
        }

        // Extract relevant sections based on keywords
        String[] keywords = extractKeywords(question);
        String relevantContent = findRelevantSections(documentContent, keywords);

        if (!relevantContent.isEmpty()) {
            return String.format(
                "**Analysis of %s**\n\n" +
                "Based on your question: \"%s\"\n\n" +
                "Here's the relevant information I found:\n\n%s\n\n" +
                "*Note: For more detailed AI analysis, please ensure your Groq API key is configured.*",
                documentName,
                question,
                relevantContent.length() > 1000 ? relevantContent.substring(0, 1000) + "..." : relevantContent
            );
        }

        return String.format(
            "I've analyzed the document '%s' regarding your question: \"%s\"\n\n" +
            "The document contains %d words of content. While I can see the document has relevant information, " +
            "I'd need the Groq API to be configured to provide you with a detailed, ChatGPT-4 level analysis.\n\n" +
            "Please configure your Groq API key to unlock the full AI capabilities of this system.",
            documentName,
            question,
            documentContent.split("\\s+").length
        );
    }

    private String generateDocumentSummaryFallback(String documentContent, String documentName) {
        String[] paragraphs = documentContent.split("\n\n");
        int wordCount = documentContent.split("\\s+").length;

        StringBuilder summary = new StringBuilder();
        summary.append("**Document Summary: ").append(documentName).append("**\n\n");
        summary.append("**Overview:**\n");
        summary.append("- Document length: ").append(wordCount).append(" words\n");
        summary.append("- Number of sections: ").append(paragraphs.length).append("\n\n");

        summary.append("**Content Preview:**\n");
        int previewParagraphs = Math.min(3, paragraphs.length);
        for (int i = 0; i < previewParagraphs; i++) {
            String paragraph = paragraphs[i].trim();
            if (!paragraph.isEmpty() && paragraph.length() > 50) {
                summary.append("• ").append(paragraph.length() > 200 ?
                    paragraph.substring(0, 200) + "..." : paragraph).append("\n\n");
            }
        }

        summary.append("*Note: For a comprehensive AI-powered summary with detailed analysis, please configure your Groq API key.*");

        return summary.toString();
    }

    private String[] extractKeywords(String question) {
        String[] stopWords = {"what", "how", "when", "where", "why", "who", "is", "are", "was", "were",
                             "the", "a", "an", "and", "or", "but", "in", "on", "at", "to", "for", "of", "with"};

        String[] words = question.toLowerCase().replaceAll("[^a-zA-Z0-9\\s]", "").split("\\s+");
        return java.util.Arrays.stream(words)
                .filter(word -> word.length() > 3)
                .filter(word -> !java.util.Arrays.asList(stopWords).contains(word))
                .toArray(String[]::new);
    }

    private String findRelevantSections(String documentContent, String[] keywords) {
        String[] paragraphs = documentContent.split("\n\n");
        StringBuilder relevantContent = new StringBuilder();

        for (String paragraph : paragraphs) {
            String lowerParagraph = paragraph.toLowerCase();
            int matchCount = 0;

            for (String keyword : keywords) {
                if (lowerParagraph.contains(keyword.toLowerCase())) {
                    matchCount++;
                }
            }

            if (matchCount >= Math.max(1, keywords.length / 2)) {
                relevantContent.append(paragraph.trim()).append("\n\n");
            }
        }

        return relevantContent.toString().trim();
    }
    
    public String generateNotes(String documentContent, String noteType) {
        String prompt = buildNotesPrompt(noteType);
        return generateResponse(prompt, documentContent, qualityModel);
    }
    
    public String generateChatResponse(String message, String context, List<Map<String, String>> chatHistory) {
        try {
            if (!isConfigured()) {
                return generateFallbackChatResponse(message, context);
            }
            
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", fastModel); // Use fast model for chat
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);
            
            ArrayNode messages = objectMapper.createArrayNode();
            
            // System message
            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", buildChatSystemPrompt(context));
            messages.add(systemMessage);
            
            // Add chat history
            if (chatHistory != null) {
                for (Map<String, String> historyMessage : chatHistory) {
                    ObjectNode histMsg = objectMapper.createObjectNode();
                    histMsg.put("role", historyMessage.get("role"));
                    histMsg.put("content", historyMessage.get("content"));
                    messages.add(histMsg);
                }
            }
            
            // Current user message
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", message);
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );
            
            Request request = new Request.Builder()
                    .url(baseUrl + "/chat/completions")
                    .addHeader("Authorization", "Bearer " + groqApiKey)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            
            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API chat request failed with status: {}", response.code());
                    return generateFallbackChatResponse(message, context);
                }
                
                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                JsonNode choices = jsonResponse.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode responseMessage = firstChoice.get("message");
                    if (responseMessage != null) {
                        return responseMessage.get("content").asText();
                    }
                }
                
                return generateFallbackChatResponse(message, context);
            }
            
        } catch (Exception e) {
            logger.error("Error in Groq chat: {}", e.getMessage());
            return generateFallbackChatResponse(message, context);
        }
    }
    
    private String buildSystemPrompt() {
        return "You are a helpful AI assistant that answers questions based on provided context. " +
               "Always base your answers on the given context. If the context doesn't contain " +
               "enough information to answer the question, say so clearly. Be concise but comprehensive.";
    }
    
    private String buildUserPrompt(String question, String context) {
        return String.format(
            "Context:\n%s\n\nQuestion: %s\n\nPlease provide a helpful answer based on the context above.",
            context,
            question
        );
    }
    
    private String buildChatSystemPrompt(String context) {
        return "You are a helpful AI assistant having a conversation about a document. " +
               "Use the following context from the document to inform your responses:\n\n" +
               context + "\n\n" +
               "Be conversational, helpful, and refer to the document content when relevant.";
    }
    
    private String buildNotesPrompt(String noteType) {
        switch (noteType.toLowerCase()) {
            case "detailed":
                return "Create comprehensive, detailed notes from the following document content. " +
                       "Include all important points, explanations, examples, and key details. " +
                       "Organize the notes with clear headings and bullet points.";
            case "quick":
                return "Create quick revision notes from the following document content. " +
                       "Focus on key points, important facts, and essential information. " +
                       "Use bullet points and keep it concise for quick review.";
            case "medium":
                return "Create medium-level notes from the following document content. " +
                       "Include important concepts, main points, and supporting details. " +
                       "Balance comprehensiveness with readability.";
            default:
                return "Create well-organized notes from the following document content.";
        }
    }
    
    private String generateFallbackResponse(String prompt, String context) {
        return String.format(
            "**Question:** %s\n\n" +
            "**Based on the document content:**\n%s\n\n" +
            "*Note: This is a basic response. For AI-powered answers, please configure your Groq API key.*",
            prompt,
            context.length() > 1000 ? context.substring(0, 1000) + "..." : context
        );
    }
    
    private String generateFallbackChatResponse(String message, String context) {
        return String.format(
            "I understand you're asking: %s\n\n" +
            "Based on the document, here's what I can tell you:\n%s\n\n" +
            "*Note: For interactive AI chat, please configure your Groq API key.*",
            message,
            context.length() > 500 ? context.substring(0, 500) + "..." : context
        );
    }
    
    public boolean isConfigured() {
        return groqApiKey != null && !groqApiKey.isEmpty();
    }
    
    public String getServiceStatus() {
        if (isConfigured()) {
            return "Groq API configured and ready";
        } else {
            return "Groq API not configured (using fallback responses)";
        }
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public String getFastModel() {
        return fastModel;
    }
    
    public String getQualityModel() {
        return qualityModel;
    }

    public String generateChatGPTLevelChatResponse(String userMessage, String documentContent, String documentName, List<Map<String, String>> chatHistory) {
        try {
            if (!isConfigured()) {
                return generateIntelligentChatFallback(userMessage, documentContent, documentName, chatHistory);
            }

            // Create a comprehensive system prompt for ChatGPT-4 level chat responses
            String systemPrompt = "You are an expert AI assistant with advanced document analysis capabilities, similar to ChatGPT-4. " +
                                "You are having a conversation with a user about a specific document. Your responses should be:\n" +
                                "- Conversational and natural, like ChatGPT-4\n" +
                                "- Detailed and informative based on the document content\n" +
                                "- Contextually aware of the conversation history\n" +
                                "- Professional yet friendly and engaging\n" +
                                "- Always grounded in the provided document content\n" +
                                "- Include specific examples, quotes, and references when relevant\n" +
                                "- Maintain conversation flow and remember previous exchanges\n" +
                                "- Use appropriate formatting when helpful\n\n" +
                                "You have access to the full document content and should provide comprehensive, insightful responses that demonstrate deep understanding of both the document and the user's questions.";

            // Build conversation context
            StringBuilder conversationContext = new StringBuilder();
            conversationContext.append("Document: ").append(documentName).append("\n\n");
            conversationContext.append("Document Content:\n").append(
                documentContent.length() > 10000 ? documentContent.substring(0, 10000) + "\n\n[Document continues...]" : documentContent
            ).append("\n\n");

            if (!chatHistory.isEmpty()) {
                conversationContext.append("Previous Conversation:\n");
                for (Map<String, String> msg : chatHistory) {
                    String role = msg.get("role");
                    String content = msg.get("content");
                    conversationContext.append(role.equals("user") ? "User: " : "Assistant: ")
                                     .append(content).append("\n");
                }
                conversationContext.append("\n");
            }

            conversationContext.append("Current User Message: ").append(userMessage).append("\n\n");
            conversationContext.append("Please provide a comprehensive, ChatGPT-4 level response based on the document content and conversation context.");

            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", qualityModel); // Use the highest quality model
            requestBody.put("max_tokens", 4096); // Increased for comprehensive responses
            requestBody.put("temperature", 0.4); // Slightly higher for more conversational responses

            ArrayNode messages = objectMapper.createArrayNode();

            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);

            ObjectNode userMessage_obj = objectMapper.createObjectNode();
            userMessage_obj.put("role", "user");
            userMessage_obj.put("content", conversationContext.toString());
            messages.add(userMessage_obj);

            requestBody.set("messages", messages);

            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );

            Request request = new Request.Builder()
                .url(baseUrl + "/chat/completions")
                .header("Authorization", "Bearer " + groqApiKey)
                .header("Content-Type", "application/json")
                .post(body)
                .build();

            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API request failed with status: {}", response.code());
                    return generateIntelligentChatFallback(userMessage, documentContent, documentName, chatHistory);
                }

                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);

                if (jsonResponse.has("choices") && jsonResponse.get("choices").size() > 0) {
                    String aiResponse = jsonResponse.get("choices").get(0)
                        .get("message").get("content").asText();

                    logger.info("Generated ChatGPT-4 level chat response for document: {}", documentName);
                    return aiResponse;
                } else {
                    logger.error("Unexpected Groq API response format");
                    return generateIntelligentChatFallback(userMessage, documentContent, documentName, chatHistory);
                }
            }

        } catch (Exception e) {
            logger.error("Error generating ChatGPT-4 level chat response: {}", e.getMessage());
            return generateIntelligentChatFallback(userMessage, documentContent, documentName, chatHistory);
        }
    }

    private String generateIntelligentChatFallback(String userMessage, String documentContent, String documentName, List<Map<String, String>> chatHistory) {
        // Provide a more intelligent fallback for chat when Groq is not available
        StringBuilder response = new StringBuilder();

        response.append("I'm analyzing your question about ").append(documentName).append(".\n\n");

        // Check if this is a follow-up question
        if (!chatHistory.isEmpty()) {
            response.append("Based on our previous conversation and your current question: \"")
                    .append(userMessage).append("\"\n\n");
        } else {
            response.append("Regarding your question: \"").append(userMessage).append("\"\n\n");
        }

        // Try to find relevant content
        String[] keywords = extractKeywords(userMessage);
        String relevantContent = findRelevantSections(documentContent, keywords);

        if (!relevantContent.isEmpty()) {
            response.append("Here's what I found in the document:\n\n");
            response.append(relevantContent.length() > 800 ? relevantContent.substring(0, 800) + "..." : relevantContent);
            response.append("\n\n");
        }

        response.append("*Note: For more detailed, ChatGPT-4 level analysis and conversation capabilities, please ensure your Groq API key is properly configured.*");

        return response.toString();
    }
}
