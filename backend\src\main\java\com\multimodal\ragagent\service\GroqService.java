package com.multimodal.ragagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.node.ArrayNode;
import com.fasterxml.jackson.databind.node.ObjectNode;
import okhttp3.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.time.Duration;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Service
public class GroqService {
    
    private static final Logger logger = LoggerFactory.getLogger(GroqService.class);
    
    @Value("${groq.api.key:}")
    private String groqApiKey;
    
    @Value("${groq.api.base-url:https://api.groq.com/openai/v1}")
    private String baseUrl;
    
    @Value("${groq.model.default:llama3-8b-8192}")
    private String defaultModel;
    
    @Value("${groq.model.fast:llama3-8b-8192}")
    private String fastModel;
    
    @Value("${groq.model.quality:llama3-70b-8192}")
    private String qualityModel;
    
    @Value("${groq.settings.max-tokens:2048}")
    private int maxTokens;
    
    @Value("${groq.settings.temperature:0.7}")
    private double temperature;
    
    @Value("${groq.settings.timeout-seconds:30}")
    private int timeoutSeconds;
    
    private final ObjectMapper objectMapper = new ObjectMapper();
    private OkHttpClient httpClient;
    
    private OkHttpClient getHttpClient() {
        if (httpClient == null) {
            httpClient = new OkHttpClient.Builder()
                    .connectTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .readTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .writeTimeout(timeoutSeconds, TimeUnit.SECONDS)
                    .build();
        }
        return httpClient;
    }
    
    public String generateResponse(String prompt, String context) {
        return generateResponse(prompt, context, defaultModel);
    }
    
    public String generateResponse(String prompt, String context, String model) {
        try {
            if (!isConfigured()) {
                return generateFallbackResponse(prompt, context);
            }
            
            String systemPrompt = buildSystemPrompt();
            String userPrompt = buildUserPrompt(prompt, context);
            
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", model);
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);
            
            ArrayNode messages = objectMapper.createArrayNode();
            
            // System message
            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", systemPrompt);
            messages.add(systemMessage);
            
            // User message
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", userPrompt);
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );
            
            Request request = new Request.Builder()
                    .url(baseUrl + "/chat/completions")
                    .addHeader("Authorization", "Bearer " + groqApiKey)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            
            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API request failed with status: {}", response.code());
                    return generateFallbackResponse(prompt, context);
                }
                
                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                JsonNode choices = jsonResponse.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode message = firstChoice.get("message");
                    if (message != null) {
                        String content = message.get("content").asText();
                        logger.info("Generated response using Groq model: {}", model);
                        return content;
                    }
                }
                
                logger.warn("Unexpected response format from Groq API");
                return generateFallbackResponse(prompt, context);
            }
            
        } catch (Exception e) {
            logger.error("Error calling Groq API: {}", e.getMessage());
            return generateFallbackResponse(prompt, context);
        }
    }
    
    public String generateRAGResponse(String question, String context, String documentName) {
        String prompt = String.format(
            "Based on the provided context from the document '%s', please answer the following question: %s",
            documentName != null ? documentName : "the uploaded document",
            question
        );
        
        return generateResponse(prompt, context, defaultModel);
    }
    
    public String generateNotes(String documentContent, String noteType) {
        String prompt = buildNotesPrompt(noteType);
        return generateResponse(prompt, documentContent, qualityModel);
    }
    
    public String generateChatResponse(String message, String context, List<Map<String, String>> chatHistory) {
        try {
            if (!isConfigured()) {
                return generateFallbackChatResponse(message, context);
            }
            
            ObjectNode requestBody = objectMapper.createObjectNode();
            requestBody.put("model", fastModel); // Use fast model for chat
            requestBody.put("max_tokens", maxTokens);
            requestBody.put("temperature", temperature);
            
            ArrayNode messages = objectMapper.createArrayNode();
            
            // System message
            ObjectNode systemMessage = objectMapper.createObjectNode();
            systemMessage.put("role", "system");
            systemMessage.put("content", buildChatSystemPrompt(context));
            messages.add(systemMessage);
            
            // Add chat history
            if (chatHistory != null) {
                for (Map<String, String> historyMessage : chatHistory) {
                    ObjectNode histMsg = objectMapper.createObjectNode();
                    histMsg.put("role", historyMessage.get("role"));
                    histMsg.put("content", historyMessage.get("content"));
                    messages.add(histMsg);
                }
            }
            
            // Current user message
            ObjectNode userMessage = objectMapper.createObjectNode();
            userMessage.put("role", "user");
            userMessage.put("content", message);
            messages.add(userMessage);
            
            requestBody.set("messages", messages);
            
            RequestBody body = RequestBody.create(
                requestBody.toString(),
                MediaType.get("application/json")
            );
            
            Request request = new Request.Builder()
                    .url(baseUrl + "/chat/completions")
                    .addHeader("Authorization", "Bearer " + groqApiKey)
                    .addHeader("Content-Type", "application/json")
                    .post(body)
                    .build();
            
            try (Response response = getHttpClient().newCall(request).execute()) {
                if (!response.isSuccessful()) {
                    logger.error("Groq API chat request failed with status: {}", response.code());
                    return generateFallbackChatResponse(message, context);
                }
                
                String responseBody = response.body().string();
                JsonNode jsonResponse = objectMapper.readTree(responseBody);
                
                JsonNode choices = jsonResponse.get("choices");
                if (choices != null && choices.size() > 0) {
                    JsonNode firstChoice = choices.get(0);
                    JsonNode responseMessage = firstChoice.get("message");
                    if (responseMessage != null) {
                        return responseMessage.get("content").asText();
                    }
                }
                
                return generateFallbackChatResponse(message, context);
            }
            
        } catch (Exception e) {
            logger.error("Error in Groq chat: {}", e.getMessage());
            return generateFallbackChatResponse(message, context);
        }
    }
    
    private String buildSystemPrompt() {
        return "You are a helpful AI assistant that answers questions based on provided context. " +
               "Always base your answers on the given context. If the context doesn't contain " +
               "enough information to answer the question, say so clearly. Be concise but comprehensive.";
    }
    
    private String buildUserPrompt(String question, String context) {
        return String.format(
            "Context:\n%s\n\nQuestion: %s\n\nPlease provide a helpful answer based on the context above.",
            context,
            question
        );
    }
    
    private String buildChatSystemPrompt(String context) {
        return "You are a helpful AI assistant having a conversation about a document. " +
               "Use the following context from the document to inform your responses:\n\n" +
               context + "\n\n" +
               "Be conversational, helpful, and refer to the document content when relevant.";
    }
    
    private String buildNotesPrompt(String noteType) {
        switch (noteType.toLowerCase()) {
            case "detailed":
                return "Create comprehensive, detailed notes from the following document content. " +
                       "Include all important points, explanations, examples, and key details. " +
                       "Organize the notes with clear headings and bullet points.";
            case "quick":
                return "Create quick revision notes from the following document content. " +
                       "Focus on key points, important facts, and essential information. " +
                       "Use bullet points and keep it concise for quick review.";
            case "medium":
                return "Create medium-level notes from the following document content. " +
                       "Include important concepts, main points, and supporting details. " +
                       "Balance comprehensiveness with readability.";
            default:
                return "Create well-organized notes from the following document content.";
        }
    }
    
    private String generateFallbackResponse(String prompt, String context) {
        return String.format(
            "**Question:** %s\n\n" +
            "**Based on the document content:**\n%s\n\n" +
            "*Note: This is a basic response. For AI-powered answers, please configure your Groq API key.*",
            prompt,
            context.length() > 1000 ? context.substring(0, 1000) + "..." : context
        );
    }
    
    private String generateFallbackChatResponse(String message, String context) {
        return String.format(
            "I understand you're asking: %s\n\n" +
            "Based on the document, here's what I can tell you:\n%s\n\n" +
            "*Note: For interactive AI chat, please configure your Groq API key.*",
            message,
            context.length() > 500 ? context.substring(0, 500) + "..." : context
        );
    }
    
    public boolean isConfigured() {
        return groqApiKey != null && !groqApiKey.isEmpty();
    }
    
    public String getServiceStatus() {
        if (isConfigured()) {
            return "Groq API configured and ready";
        } else {
            return "Groq API not configured (using fallback responses)";
        }
    }
    
    public String getDefaultModel() {
        return defaultModel;
    }
    
    public String getFastModel() {
        return fastModel;
    }
    
    public String getQualityModel() {
        return qualityModel;
    }
}
