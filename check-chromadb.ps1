#!/usr/bin/env powershell

Write-Host "==================================="
Write-Host "   CHROMADB VECTOR DATABASE VIEWER"
Write-Host "==================================="
Write-Host ""

try {
    Write-Host "🔍 Checking ChromaDB Collections..."
    $collections = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/collections" -Method GET
    
    if ($collections -and $collections.Count -gt 0) {
        Write-Host "✅ Found $($collections.Count) collection(s):" -ForegroundColor Green
        Write-Host ""
        
        foreach ($collection in $collections) {
            Write-Host "📁 Collection: $($collection.name)" -ForegroundColor Cyan
            Write-Host "   ID: $($collection.id)"
            Write-Host "   Metadata: $($collection.metadata | ConvertTo-Json -Compress)"
            Write-Host ""
            
            # Get collection details
            try {
                $collectionDetails = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/collections/$($collection.name)" -Method GET
                Write-Host "   📊 Collection Details:"
                Write-Host "      Documents: $($collectionDetails.count)"
                Write-Host "      Dimension: $($collectionDetails.dimension)"
                Write-Host ""
                
                # Get some sample documents
                $query = @{
                    limit = 5
                } | ConvertTo-Json
                
                $documents = Invoke-RestMethod -Uri "http://localhost:8000/api/v1/collections/$($collection.name)/get" -Method POST -Body $query -ContentType "application/json"
                
                if ($documents.ids -and $documents.ids.Count -gt 0) {
                    Write-Host "   📄 Sample Documents (first 5):"
                    for ($i = 0; $i -lt [Math]::Min(5, $documents.ids.Count); $i++) {
                        Write-Host "      ID: $($documents.ids[$i])"
                        if ($documents.metadatas -and $documents.metadatas[$i]) {
                            Write-Host "      Metadata: $($documents.metadatas[$i] | ConvertTo-Json -Compress)"
                        }
                        if ($documents.documents -and $documents.documents[$i]) {
                            $text = $documents.documents[$i]
                            if ($text.Length -gt 100) {
                                $text = $text.Substring(0, 100) + "..."
                            }
                            Write-Host "      Text: $text"
                        }
                        if ($documents.embeddings -and $documents.embeddings[$i]) {
                            Write-Host "      Embedding: [Vector with $($documents.embeddings[$i].Count) dimensions]"
                        }
                        Write-Host ""
                    }
                } else {
                    Write-Host "   ⚠️  No documents found in this collection" -ForegroundColor Yellow
                }
                
            } catch {
                Write-Host "   ❌ Error getting collection details: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "⚠️  No collections found in ChromaDB" -ForegroundColor Yellow
        Write-Host "   This means no documents have been uploaded and processed yet."
    }
    
} catch {
    Write-Host "❌ Error connecting to ChromaDB: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "   Make sure ChromaDB is running on http://localhost:8000"
}

Write-Host ""
Write-Host "==================================="
Write-Host "To add embeddings:"
Write-Host "1. Upload a document via the web interface"
Write-Host "2. The system will automatically chunk and embed it"
Write-Host "3. Run this script again to see the stored vectors"
Write-Host "==================================="
