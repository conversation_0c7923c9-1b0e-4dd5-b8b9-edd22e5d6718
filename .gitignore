# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
node_modules/
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next
out

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Java
*.class
*.jar
*.war
*.ear
*.nar
hs_err_pid*

# Maven
target/
pom.xml.tag
pom.xml.releaseBackup
pom.xml.versionsBackup
pom.xml.next
release.properties
dependency-reduced-pom.xml
buildNumber.properties
.mvn/timing.properties
.mvn/wrapper/maven-wrapper.jar

# Gradle
.gradle
build/
!gradle/wrapper/gradle-wrapper.jar
!**/src/main/**/build/
!**/src/test/**/build/

# IntelliJ IDEA
.idea
*.iws
*.iml
*.ipr
out/
!**/src/main/**/out/
!**/src/test/**/out/

# Eclipse
.apt_generated
.classpath
.factorypath
.project
.settings
.springBeans
.sts4-cache
bin/
!**/src/main/**/bin/
!**/src/test/**/bin/

# NetBeans
/nbproject/private/
/nbbuild/
/dist/
/nbdist/
/.nb-gradle/

# VS Code
.vscode/

# File uploads
uploads/
*.pdf
*.doc
*.docx
*.ppt
*.pptx

# Database
*.db
*.sqlite
*.sqlite3

# Vector database data
chroma_data/
vector_db/

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Docker
.dockerignore

# Project specific files
*.ps1
*.bat
test-*.txt
test-*.json

# API Keys and sensitive data (extra protection)
**/application-*.yml
**/application-*.properties
**/.env*
**/secrets/
**/*secret*
**/*key*
**/*token*

# Build and compiled files
**/target/
**/build/
**/dist/
**/out/

# IDE and editor files
**/.vscode/
**/.idea/
*.iml
*.ipr
*.iws

# Temporary test files
test-document.*
test-embedding.*
test-hf-*.*
simple-chromadb-check.*
check-chromadb.*

# Application data
uploads/
documents/
notes/
chroma_data/
vector_db/
data/

# Logs and runtime
**/*.log
**/logs/
**/*.pid

# OS specific
.DS_Store
Thumbs.db
*.tmp
*.temp

# Package managers
**/node_modules/
**/.npm/
**/yarn.lock
**/package-lock.json

# Spring Boot specific
**/HELP.md
**/mvnw
**/mvnw.cmd
**/.mvn/wrapper/maven-wrapper.jar
**/.mvn/wrapper/maven-wrapper.properties
