package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.RAGService;
import com.multimodal.ragagent.service.GroqService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.Map;

@RestController
@RequestMapping("/api/rag")
@CrossOrigin(origins = "*", maxAge = 3600)
public class RAGController {
    
    @Autowired
    private RAGService ragService;

    @Autowired
    private GroqService groqService;
    
    @PostMapping("/query")
    public ResponseEntity<?> queryDocument(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            Long documentId = Long.valueOf(request.get("documentId").toString());
            String question = request.get("question").toString();
            
            RAGService.RAGResponse response = ragService.queryDocument(documentId, question, user);
            
            Map<String, Object> result = new HashMap<>();
            result.put("answer", response.getAnswer());
            result.put("success", response.isSuccess());
            
            if (response.getSourceChunks() != null) {
                result.put("sourceChunks", response.getSourceChunks().stream()
                    .map(chunk -> {
                        Map<String, Object> chunkInfo = new HashMap<>();
                        chunkInfo.put("id", chunk.getId());
                        chunkInfo.put("content", chunk.getContent());
                        chunkInfo.put("chunkIndex", chunk.getChunkIndex());
                        chunkInfo.put("documentName", chunk.getDocument().getOriginalFilename());
                        return chunkInfo;
                    })
                    .toList());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to process query: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PostMapping("/query-all")
    public ResponseEntity<?> queryAllDocuments(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            String question = request.get("question").toString();
            
            RAGService.RAGResponse response = ragService.queryAllDocuments(question, user);
            
            Map<String, Object> result = new HashMap<>();
            result.put("answer", response.getAnswer());
            result.put("success", response.isSuccess());
            
            if (response.getSourceChunks() != null) {
                result.put("sourceChunks", response.getSourceChunks().stream()
                    .map(chunk -> {
                        Map<String, Object> chunkInfo = new HashMap<>();
                        chunkInfo.put("id", chunk.getId());
                        chunkInfo.put("content", chunk.getContent());
                        chunkInfo.put("chunkIndex", chunk.getChunkIndex());
                        chunkInfo.put("documentName", chunk.getDocument().getOriginalFilename());
                        return chunkInfo;
                    })
                    .toList());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to process query: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/status")
    public ResponseEntity<?> getRAGStatus(@AuthenticationPrincipal User user) {
        Map<String, Object> status = new HashMap<>();
        status.put("available", ragService.isRAGAvailable());
        status.put("vectorDatabase", ragService.getRAGStatus());
        status.put("groqService", groqService.getServiceStatus());
        status.put("groqConfigured", groqService.isConfigured());

        return ResponseEntity.ok(status);
    }
    
    @PostMapping("/chat")
    public ResponseEntity<?> chatWithDocument(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            Long documentId = Long.valueOf(request.get("documentId").toString());
            @SuppressWarnings("unchecked")
            java.util.List<Map<String, String>> messages = 
                (java.util.List<Map<String, String>>) request.get("messages");
            
            // For now, just process the last message as a query
            if (messages.isEmpty()) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "No messages provided");
                return ResponseEntity.badRequest().body(error);
            }
            
            Map<String, String> lastMessage = messages.get(messages.size() - 1);
            String question = lastMessage.get("content");
            
            RAGService.RAGResponse response = ragService.queryDocument(documentId, question, user);
            
            Map<String, Object> result = new HashMap<>();
            result.put("message", response.getAnswer());
            result.put("success", response.isSuccess());
            
            if (response.getSourceChunks() != null) {
                result.put("sourceChunks", response.getSourceChunks().stream()
                    .map(chunk -> {
                        Map<String, Object> chunkInfo = new HashMap<>();
                        chunkInfo.put("id", chunk.getId());
                        chunkInfo.put("content", chunk.getContent().substring(0, 
                            Math.min(chunk.getContent().length(), 200)) + "...");
                        chunkInfo.put("documentName", chunk.getDocument().getOriginalFilename());
                        return chunkInfo;
                    })
                    .toList());
            }
            
            return ResponseEntity.ok(result);
            
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to process chat: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
}
