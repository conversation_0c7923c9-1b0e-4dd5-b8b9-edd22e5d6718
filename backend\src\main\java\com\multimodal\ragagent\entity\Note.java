package com.multimodal.ragagent.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;

import java.time.LocalDateTime;

@Entity
@Table(name = "notes", indexes = {
    @Index(name = "idx_note_user_id", columnList = "user_id"),
    @Index(name = "idx_note_document_id", columnList = "document_id"),
    @Index(name = "idx_note_type", columnList = "note_type"),
    @Index(name = "idx_note_status", columnList = "status"),
    @Index(name = "idx_note_created_at", columnList = "created_at"),
    @Index(name = "idx_note_user_document", columnList = "user_id, document_id")
})
public class Note {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @NotBlank
    private String title;
    
    @NotBlank
    @Column(columnDefinition = "TEXT")
    private String content;
    
    @Enumerated(EnumType.STRING)
    @Column(name = "note_type")
    private NoteType noteType;
    
    @Enumerated(EnumType.STRING)
    private NoteStatus status = NoteStatus.DRAFT;
    
    @Column(name = "pdf_path")
    private String pdfPath;
    
    @Column(name = "created_at")
    private LocalDateTime createdAt;
    
    @Column(name = "updated_at")
    private LocalDateTime updatedAt;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", nullable = false)
    @JsonIgnore
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "document_id", nullable = false)
    private Document document;
    
    public enum NoteType {
        DETAILED, QUICK_REVISION, MEDIUM_LEVEL,
        SUMMARY, FLASHCARDS, QNA, OUTLINE,
        MINDMAP, STUDY_GUIDE, KEY_POINTS
    }
    
    public enum NoteStatus {
        DRAFT, PUBLISHED, ARCHIVED
    }
    
    // Constructors
    public Note() {}
    
    public Note(String title, String content, NoteType noteType, User user, Document document) {
        this.title = title;
        this.content = content;
        this.noteType = noteType;
        this.user = user;
        this.document = document;
        this.createdAt = LocalDateTime.now();
        this.updatedAt = LocalDateTime.now();
    }
    
    // Lifecycle callbacks
    @PrePersist
    protected void onCreate() {
        createdAt = LocalDateTime.now();
        updatedAt = LocalDateTime.now();
    }
    
    @PreUpdate
    protected void onUpdate() {
        updatedAt = LocalDateTime.now();
    }
    
    // Getters and Setters
    public Long getId() { return id; }
    public void setId(Long id) { this.id = id; }
    
    public String getTitle() { return title; }
    public void setTitle(String title) { this.title = title; }
    
    public String getContent() { return content; }
    public void setContent(String content) { this.content = content; }
    
    public NoteType getNoteType() { return noteType; }
    public void setNoteType(NoteType noteType) { this.noteType = noteType; }
    
    public NoteStatus getStatus() { return status; }
    public void setStatus(NoteStatus status) { this.status = status; }
    
    public String getPdfPath() { return pdfPath; }
    public void setPdfPath(String pdfPath) { this.pdfPath = pdfPath; }
    
    public LocalDateTime getCreatedAt() { return createdAt; }
    public void setCreatedAt(LocalDateTime createdAt) { this.createdAt = createdAt; }
    
    public LocalDateTime getUpdatedAt() { return updatedAt; }
    public void setUpdatedAt(LocalDateTime updatedAt) { this.updatedAt = updatedAt; }
    
    public User getUser() { return user; }
    public void setUser(User user) { this.user = user; }
    
    public Document getDocument() { return document; }
    public void setDocument(Document document) { this.document = document; }
}
