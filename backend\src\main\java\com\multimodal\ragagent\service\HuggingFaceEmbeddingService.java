package com.multimodal.ragagent.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.*;
import java.util.stream.Collectors;

@Service
public class HuggingFaceEmbeddingService {
    
    private static final Logger logger = LoggerFactory.getLogger(HuggingFaceEmbeddingService.class);
    
    @Value("${huggingface.api.key:}")
    private String apiKey;
    
    @Value("${huggingface.model:sentence-transformers/all-MiniLM-L6-v2}")
    private String model;
    
    @Value("${huggingface.api.url:https://api-inference.huggingface.co/pipeline/feature-extraction}")
    private String apiUrl;
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public List<Double> generateEmbedding(String text) {
        if (!isConfigured()) {
            logger.warn("Hugging Face API key not configured");
            return generateMockEmbedding(text);
        }
        
        try {
            String url = apiUrl + "/" + model;
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + apiKey);
            headers.setContentType(MediaType.APPLICATION_JSON);
            
            Map<String, Object> request = new HashMap<>();
            request.put("inputs", text);
            request.put("options", Map.of("wait_for_model", true));
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
            
            logger.debug("Generating Hugging Face embedding for text of length: {}", text.length());
            
            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);
            
            if (response.getStatusCode() == HttpStatus.OK) {
                JsonNode jsonResponse = objectMapper.readTree(response.getBody());
                
                List<Double> embedding = new ArrayList<>();
                if (jsonResponse.isArray() && jsonResponse.size() > 0) {
                    JsonNode embeddingArray = jsonResponse.get(0);
                    if (embeddingArray.isArray()) {
                        for (JsonNode value : embeddingArray) {
                            embedding.add(value.asDouble());
                        }
                    }
                }
                
                if (embedding.isEmpty()) {
                    logger.warn("Empty embedding returned from Hugging Face, using mock");
                    return generateMockEmbedding(text);
                }
                
                logger.debug("Generated Hugging Face embedding with {} dimensions", embedding.size());
                return embedding;
                
            } else {
                logger.error("Hugging Face API returned status: {}", response.getStatusCode());
                return generateMockEmbedding(text);
            }
            
        } catch (Exception e) {
            logger.error("Failed to generate Hugging Face embedding: {}", e.getMessage());
            return generateMockEmbedding(text);
        }
    }
    
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        if (!isConfigured()) {
            logger.warn("Hugging Face API key not configured, using mock embeddings");
            return texts.stream()
                    .map(this::generateMockEmbedding)
                    .collect(Collectors.toList());
        }
        
        List<List<Double>> embeddings = new ArrayList<>();
        
        try {
            // Process in batches to avoid API limits
            int batchSize = 10;
            for (int i = 0; i < texts.size(); i += batchSize) {
                int endIndex = Math.min(i + batchSize, texts.size());
                List<String> batch = texts.subList(i, endIndex);
                
                for (String text : batch) {
                    embeddings.add(generateEmbedding(text));
                    
                    // Small delay to respect rate limits
                    Thread.sleep(100);
                }
            }
            
            logger.info("Generated {} Hugging Face embeddings", embeddings.size());
            return embeddings;
            
        } catch (Exception e) {
            logger.error("Failed to generate Hugging Face embeddings: {}", e.getMessage());
            // Fallback to mock embeddings
            return texts.stream()
                    .map(this::generateMockEmbedding)
                    .collect(Collectors.toList());
        }
    }
    
    public boolean isConfigured() {
        return apiKey != null && !apiKey.trim().isEmpty() && !apiKey.equals("your-huggingface-api-key");
    }
    
    public String getEmbeddingModel() {
        return model;
    }
    
    private List<Double> generateMockEmbedding(String text) {
        // Generate a consistent mock embedding based on text hash
        Random random = new Random(text.hashCode());
        List<Double> embedding = new ArrayList<>();
        
        // all-MiniLM-L6-v2 produces 384-dimensional embeddings
        for (int i = 0; i < 384; i++) {
            embedding.add(random.nextGaussian());
        }
        
        return embedding;
    }
    
    public boolean testConnection() {
        try {
            List<Double> testEmbedding = generateEmbedding("test connection");
            return testEmbedding != null && !testEmbedding.isEmpty();
        } catch (Exception e) {
            logger.error("Hugging Face connection test failed: {}", e.getMessage());
            return false;
        }
    }
}
