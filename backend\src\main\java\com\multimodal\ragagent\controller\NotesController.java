package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.NotesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/notes")
@CrossOrigin(origins = "*", maxAge = 3600)
public class NotesController {
    
    @Autowired
    private NotesService notesService;
    
    @GetMapping
    public ResponseEntity<Page<Note>> getAllNotes(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") 
            ? Sort.by(sortBy).descending() 
            : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Note> notes = notesService.getUserNotes(user, pageable);
        
        return ResponseEntity.ok(notes);
    }
    
    @GetMapping("/types")
    public ResponseEntity<?> getNoteTypes() {
        Map<String, Object> response = new HashMap<>();
        response.put("types", List.of("DETAILED", "QUICK_REVISION", "MEDIUM_LEVEL"));
        return ResponseEntity.ok(response);
    }

    @GetMapping("/status")
    public ResponseEntity<?> getNotesStatus(@AuthenticationPrincipal User user) {
        Map<String, Object> response = new HashMap<>();
        long totalNotes = notesService.getUserNotesCount(user);
        response.put("totalNotes", totalNotes);
        response.put("status", "active");
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getNote(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Note note = notesService.getNoteById(id, user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PostMapping("/generate")
    public ResponseEntity<?> generateNote(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            Long documentId = Long.valueOf(request.get("documentId").toString());
            String noteType = request.get("noteType").toString();
            
            Note note = notesService.generateNote(documentId, Note.NoteType.valueOf(noteType), user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to generate note: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<?> updateNote(
            @PathVariable Long id,
            @RequestBody Map<String, Object> updates,
            @AuthenticationPrincipal User user) {
        try {
            Note note = notesService.updateNote(id, updates, user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteNote(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            notesService.deleteNote(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Note deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/{id}/export/pdf")
    public ResponseEntity<?> exportNoteToPdf(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            // For now, return a placeholder response
            Map<String, String> response = new HashMap<>();
            response.put("message", "PDF export not yet implemented");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<Note>> searchNotes(
            @RequestParam String q,
            @AuthenticationPrincipal User user) {
        List<Note> notes = notesService.searchNotes(q, user);
        return ResponseEntity.ok(notes);
    }
}
