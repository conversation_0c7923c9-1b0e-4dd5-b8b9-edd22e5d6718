package com.multimodal.ragagent.controller;

import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.service.NotesService;
import com.multimodal.ragagent.service.NotesGenerationService;
import com.multimodal.ragagent.service.PdfExportService;
import com.multimodal.ragagent.service.NoteTemplateService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.annotation.AuthenticationPrincipal;
import org.springframework.web.bind.annotation.*;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/notes")
@CrossOrigin(origins = "*", maxAge = 3600)
public class NotesController {
    
    @Autowired
    private NotesService notesService;

    @Autowired
    private NotesGenerationService notesGenerationService;

    @Autowired
    private PdfExportService pdfExportService;

    @Autowired
    private NoteTemplateService noteTemplateService;
    
    @GetMapping
    public ResponseEntity<Page<Note>> getAllNotes(
            @AuthenticationPrincipal User user,
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(defaultValue = "createdAt") String sortBy,
            @RequestParam(defaultValue = "desc") String sortDir) {
        
        Sort sort = sortDir.equalsIgnoreCase("desc") 
            ? Sort.by(sortBy).descending() 
            : Sort.by(sortBy).ascending();
        
        Pageable pageable = PageRequest.of(page, size, sort);
        Page<Note> notes = notesService.getUserNotes(user, pageable);
        
        return ResponseEntity.ok(notes);
    }
    
    @GetMapping("/types")
    public ResponseEntity<?> getNoteTypes() {
        Map<String, Object> response = new HashMap<>();

        // Create detailed type information
        List<Map<String, String>> types = new ArrayList<>();

        // Basic types
        types.add(Map.of("code", "DETAILED", "name", "Detailed Notes", "description", "Comprehensive notes with all important details", "category", "Basic"));
        types.add(Map.of("code", "MEDIUM_LEVEL", "name", "Medium Level Notes", "description", "Balanced notes with key concepts", "category", "Basic"));
        types.add(Map.of("code", "QUICK_REVISION", "name", "Quick Revision Notes", "description", "Concise notes for quick review", "category", "Basic"));

        // Advanced types
        types.add(Map.of("code", "SUMMARY", "name", "Executive Summary", "description", "High-level overview and key takeaways", "category", "Advanced"));
        types.add(Map.of("code", "KEY_POINTS", "name", "Key Points", "description", "Essential facts and important highlights", "category", "Advanced"));
        types.add(Map.of("code", "OUTLINE", "name", "Structured Outline", "description", "Hierarchical outline of main topics", "category", "Advanced"));

        // Study aids
        types.add(Map.of("code", "FLASHCARDS", "name", "Flashcards", "description", "Question-answer pairs for active recall", "category", "Study Aids"));
        types.add(Map.of("code", "QNA", "name", "Q&A Study Guide", "description", "Questions and answers for exam preparation", "category", "Study Aids"));
        types.add(Map.of("code", "STUDY_GUIDE", "name", "Study Guide", "description", "Comprehensive study material with exercises", "category", "Study Aids"));

        // Visual
        types.add(Map.of("code", "MINDMAP", "name", "Mind Map", "description", "Visual concept map with relationships", "category", "Visual"));

        response.put("types", types);
        response.put("categories", List.of("Basic", "Advanced", "Study Aids", "Visual"));

        return ResponseEntity.ok(response);
    }

    @GetMapping("/templates")
    public ResponseEntity<?> getNoteTemplates(@RequestParam(required = false) String category) {
        Map<String, Object> response = new HashMap<>();

        if (category != null && !category.trim().isEmpty()) {
            response.put("templates", noteTemplateService.getTemplatesByCategory(category));
        } else {
            response.put("templates", noteTemplateService.getAllTemplates());
        }

        response.put("categories", noteTemplateService.getCategories());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/templates/{templateId}")
    public ResponseEntity<?> getTemplateInfo(@PathVariable String templateId) {
        Map<String, Object> templateInfo = noteTemplateService.getTemplateInfo(templateId);

        if (templateInfo == null) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Template not found: " + templateId);
            return ResponseEntity.notFound().build();
        }

        return ResponseEntity.ok(templateInfo);
    }

    @GetMapping("/status")
    public ResponseEntity<?> getNotesStatus(@AuthenticationPrincipal User user) {
        Map<String, Object> response = new HashMap<>();
        long totalNotes = notesService.getUserNotesCount(user);
        response.put("totalNotes", totalNotes);
        response.put("status", "active");
        response.put("aiAvailable", notesGenerationService.isAINotesAvailable());
        response.put("serviceStatus", notesGenerationService.getServiceStatus());
        return ResponseEntity.ok(response);
    }

    @GetMapping("/{id}")
    public ResponseEntity<?> getNote(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            Note note = notesService.getNoteById(id, user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PostMapping("/generate")
    public ResponseEntity<?> generateNote(
            @RequestBody Map<String, Object> request,
            @AuthenticationPrincipal User user) {
        try {
            Long documentId = Long.valueOf(request.get("documentId").toString());
            String noteType = request.get("noteType").toString();
            
            Note note = notesService.generateNote(documentId, Note.NoteType.valueOf(noteType), user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to generate note: " + e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<?> updateNote(
            @PathVariable Long id,
            @RequestBody Map<String, Object> updates,
            @AuthenticationPrincipal User user) {
        try {
            Note note = notesService.updateNote(id, updates, user);
            return ResponseEntity.ok(note);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<?> deleteNote(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            notesService.deleteNote(id, user);
            Map<String, String> response = new HashMap<>();
            response.put("message", "Note deleted successfully");
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", e.getMessage());
            return ResponseEntity.badRequest().body(error);
        }
    }
    
    @GetMapping("/{id}/export/pdf")
    public ResponseEntity<?> exportNoteToPdf(
            @PathVariable Long id,
            @AuthenticationPrincipal User user) {
        try {
            if (!pdfExportService.isPdfExportEnabled()) {
                Map<String, String> error = new HashMap<>();
                error.put("error", "PDF export is disabled");
                return ResponseEntity.badRequest().body(error);
            }

            Note note = notesService.getNoteById(id, user);
            byte[] pdfBytes = pdfExportService.exportNoteToPdf(note);

            return ResponseEntity.ok()
                .header("Content-Type", "application/pdf")
                .header("Content-Disposition", "attachment; filename=\"" +
                       sanitizeFilename(note.getTitle()) + ".pdf\"")
                .header("Content-Length", String.valueOf(pdfBytes.length))
                .body(pdfBytes);

        } catch (Exception e) {
            Map<String, String> error = new HashMap<>();
            error.put("error", "Failed to export PDF: " + e.getMessage());
            return ResponseEntity.internalServerError().body(error);
        }
    }

    private String sanitizeFilename(String filename) {
        if (filename == null) return "note";
        return filename.replaceAll("[^a-zA-Z0-9._-]", "_").substring(0, Math.min(filename.length(), 100));
    }
    
    @GetMapping("/search")
    public ResponseEntity<List<Note>> searchNotes(
            @RequestParam String q,
            @AuthenticationPrincipal User user) {
        List<Note> notes = notesService.searchNotes(q, user);
        return ResponseEntity.ok(notes);
    }
}
