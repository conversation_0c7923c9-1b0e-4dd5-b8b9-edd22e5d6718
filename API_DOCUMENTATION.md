# Multimodal RAG Agent API Documentation

## Overview

The Multimodal RAG Agent provides a comprehensive REST API for document processing, AI-powered chat, and intelligent note generation. This API supports multimodal document processing with advanced RAG capabilities.

## Base URL

- **Development**: `http://localhost:8081`
- **Production**: `https://api.ragagent.com`

## Authentication

All API endpoints require JWT authentication. Include the token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

## Interactive API Documentation

Once the application is running, you can access the interactive Swagger UI documentation at:

- **Swagger UI**: `http://localhost:8081/swagger-ui.html`
- **OpenAPI Spec**: `http://localhost:8081/v3/api-docs`

## Rate Limiting

The API implements rate limiting to ensure fair usage:

- **General API**: 100 requests per minute
- **Authentication**: 10 requests per minute
- **File Upload**: 20 uploads per hour
- **Notes Generation**: 30 generations per hour
- **Chat**: 200 messages per hour
- **RAG Queries**: 100 queries per hour

Rate limit headers are included in responses:
- `X-Rate-Limit-Remaining`: Number of requests remaining
- `X-Rate-Limit-Retry-After-Seconds`: Seconds to wait before retrying

## Core Endpoints

### Authentication

#### POST /api/auth/register
Register a new user account.

**Request Body:**
```json
{
  "username": "string",
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string"
}
```

#### POST /api/auth/login
Authenticate and receive JWT token.

**Request Body:**
```json
{
  "username": "string",
  "password": "string"
}
```

### Documents

#### POST /api/documents/upload
Upload a document for processing.

**Parameters:**
- `file`: Multipart file (PDF, DOC, DOCX, PPT, PPTX, TXT, CSV)
- Maximum file size: 50MB

**Response:**
```json
{
  "message": "Document uploaded successfully",
  "data": {
    "id": 1,
    "originalFilename": "document.pdf",
    "fileType": "PDF",
    "fileSize": 1024,
    "processingStatus": "PENDING",
    "uploadedAt": "2024-01-01T12:00:00Z"
  }
}
```

#### GET /api/documents
Get paginated list of user documents.

**Query Parameters:**
- `page`: Page number (default: 0)
- `size`: Page size (default: 10)
- `sort`: Sort field (default: createdAt)
- `direction`: Sort direction (ASC/DESC, default: DESC)

#### GET /api/documents/{id}
Get specific document details.

#### DELETE /api/documents/{id}
Delete a document and all associated data.

#### GET /api/documents/{id}/status
Get document processing status and statistics.

### Notes Generation

#### POST /api/notes/generate
Generate notes from a document.

**Request Body:**
```json
{
  "documentId": 1,
  "noteType": "DETAILED",
  "customPrompt": "Focus on key concepts and examples"
}
```

**Note Types:**
- `DETAILED`: Comprehensive detailed notes
- `QUICK_REVISION`: Quick revision notes
- `MEDIUM_LEVEL`: Medium-level summary
- `SUMMARY`: Brief summary
- `FLASHCARDS`: Question-answer flashcards
- `QNA`: Question and answer format
- `OUTLINE`: Structured outline
- `MINDMAP`: Mind map structure
- `STUDY_GUIDE`: Study guide format
- `KEY_POINTS`: Key points extraction

#### GET /api/notes
Get user's generated notes.

#### GET /api/notes/{id}
Get specific note details.

#### DELETE /api/notes/{id}
Delete a note.

### Chat System

#### POST /api/chat/sessions
Create a new chat session with a document.

**Request Body:**
```json
{
  "documentId": 1,
  "title": "Chat about Document"
}
```

#### GET /api/chat/sessions
Get user's chat sessions.

#### POST /api/chat/sessions/{sessionId}/messages
Send a message in a chat session.

**Request Body:**
```json
{
  "content": "What are the main topics in this document?"
}
```

#### GET /api/chat/sessions/{sessionId}/messages
Get messages from a chat session.

### RAG System

#### POST /api/rag/query
Perform a RAG query across documents.

**Request Body:**
```json
{
  "query": "What is machine learning?",
  "documentId": 1,
  "maxResults": 5
}
```

#### GET /api/rag/status
Get RAG system status and health.

## Error Responses

All error responses follow this format:

```json
{
  "error": "Error message",
  "message": "Detailed error description",
  "status": 400,
  "timestamp": "2024-01-01T12:00:00Z"
}
```

### Common HTTP Status Codes

- `200 OK`: Request successful
- `201 Created`: Resource created successfully
- `400 Bad Request`: Invalid request data
- `401 Unauthorized`: Authentication required
- `403 Forbidden`: Access denied
- `404 Not Found`: Resource not found
- `429 Too Many Requests`: Rate limit exceeded
- `500 Internal Server Error`: Server error

## Security Features

- **JWT Authentication**: Secure token-based authentication
- **Rate Limiting**: Protection against abuse
- **Input Validation**: Comprehensive input sanitization
- **File Security**: Safe file upload with type and size validation
- **Security Headers**: CORS, CSP, and other security headers
- **SQL Injection Protection**: Parameterized queries and validation

## Testing

Run the test suite:

```bash
# Unit tests
./mvnw test

# Integration tests
./mvnw test -Dtest=**/*IntegrationTest

# All tests with coverage
./mvnw test jacoco:report
```

## Support

For API support and questions:
- **Email**: <EMAIL>
- **Documentation**: [GitHub Repository](https://github.com/ragagent/multimodal-rag)
- **Issues**: [GitHub Issues](https://github.com/ragagent/multimodal-rag/issues)
