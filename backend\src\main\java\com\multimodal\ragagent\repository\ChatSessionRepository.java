package com.multimodal.ragagent.repository;

import com.multimodal.ragagent.entity.ChatSession;
import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.User;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ChatSessionRepository extends JpaRepository<ChatSession, Long> {
    
    List<ChatSession> findByUserAndIsActiveOrderByUpdatedAtDesc(User user, Boolean isActive);
    
    Page<ChatSession> findByUserAndIsActiveOrderByUpdatedAtDesc(User user, Boolean isActive, Pageable pageable);
    
    List<ChatSession> findByDocumentAndUserAndIsActiveOrderByUpdatedAtDesc(Document document, User user, Boolean isActive);
    
    Optional<ChatSession> findByIdAndUser(Long id, User user);
    
    @Query("SELECT cs FROM ChatSession cs WHERE cs.user = :user AND cs.isActive = true ORDER BY cs.updatedAt DESC")
    List<ChatSession> findActiveSessionsByUser(@Param("user") User user);
    
    @Query("SELECT cs FROM ChatSession cs WHERE cs.document = :document AND cs.user = :user AND cs.isActive = true ORDER BY cs.updatedAt DESC")
    List<ChatSession> findActiveSessionsByDocumentAndUser(@Param("document") Document document, @Param("user") User user);
    
    @Query("SELECT COUNT(cs) FROM ChatSession cs WHERE cs.user = :user AND cs.isActive = true")
    long countActiveSessionsByUser(@Param("user") User user);
    
    @Query("SELECT COUNT(cs) FROM ChatSession cs WHERE cs.document = :document AND cs.user = :user AND cs.isActive = true")
    long countActiveSessionsByDocumentAndUser(@Param("document") Document document, @Param("user") User user);
    
    void deleteByDocument(Document document);
}
