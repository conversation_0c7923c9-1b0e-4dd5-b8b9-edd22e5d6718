# Multimodal RAG Agent for Students

A comprehensive multimodal Retrieval-Augmented Generation (RAG) system designed to help students create study notes from various document types including PDFs, PowerPoints, Word documents, CSVs, and text files.

## Features

### Core Functionality
- **Multimodal Document Processing**: Support for PDF, PPT, DOC, DOCX, CSV, TXT files
- **Intelligent Text Extraction**: Extract text, images, tables, and handwritten content
- **Vector Database Storage**: Efficient similarity search using embeddings
- **Smart RAG System**: Accurate question-answering using Groq API
- **JWT Authentication**: Secure user authentication and authorization

### Study Tools
- **Talk to Document**: Interactive chat interface for document Q&A
- **Smart Notes Generation**:
  - **Detailed Notes**: Comprehensive explanations with diagrams and real-world examples
  - **Quick Revision**: Concise bullet points for rapid review
  - **Medium Level**: Balanced notes with moderate detail and images
- **PDF Export**: Download generated notes as PDF
- **Notes Management**: Save and organize notes on the platform

### User Experience
- **Drag & Drop Upload**: Easy file upload with progress indicators
- **Dashboard**: View all generated notes in card format
- **Search & Filter**: Find specific notes quickly
- **Responsive Design**: Works on desktop and mobile devices

## Technology Stack

### Backend
- **Framework**: Spring Boot 3.x
- **Database**: PostgreSQL
- **Authentication**: JWT
- **Document Processing**: Apache PDFBox, Apache POI
- **OCR**: Tesseract (for handwritten text)
- **Vector Database**: Chroma/Qdrant
- **LLM API**: Groq API
- **Embeddings**: Sentence Transformers, CLIP

### Frontend
- **Framework**: Next.js 14 with TypeScript
- **Styling**: Tailwind CSS
- **UI Components**: Shadcn/ui
- **State Management**: Zustand
- **HTTP Client**: Axios

### Infrastructure
- **Containerization**: Docker & Docker Compose
- **Database**: PostgreSQL
- **File Storage**: Local/Cloud storage

## Project Structure

```
multimodal-rag-agent/
├── backend/                 # Spring Boot application
│   ├── src/main/java/
│   ├── src/main/resources/
│   ├── src/test/
│   └── pom.xml
├── frontend/               # Next.js application
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── next.config.js
├── docker-compose.yml      # Development environment
├── .env.example           # Environment variables template
└── README.md
```

## Getting Started

### Prerequisites
- Java 17+
- Node.js 18+
- PostgreSQL 14+
- Docker (optional)

### Environment Setup
1. Clone the repository
2. Copy `.env.example` to `.env` and configure variables
3. Set up PostgreSQL database
4. Configure Groq API key

### Running the Application

#### Using Docker (Recommended)
```bash
docker-compose up -d
```

#### Manual Setup
1. **Backend**:
   ```bash
   cd backend
   ./mvnw spring-boot:run
   ```

2. **Frontend**:
   ```bash
   cd frontend
   npm install
   npm run dev
   ```

## API Documentation

The backend provides RESTful APIs for:
- User authentication
- Document upload and processing
- RAG query processing
- Notes generation and management

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests
5. Submit a pull request

## License

This project is licensed under the MIT License.
