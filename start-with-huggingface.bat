@echo off
echo Starting Multimodal RAG Agent with Hugging Face Embeddings...

REM Set your Hugging Face API key here (get from https://huggingface.co/settings/tokens)
REM IMPORTANT: Make sure your token has "Inference" permissions enabled!
REM Required permissions: Repositories (Read) + Inference (Make calls to Inference Providers)
set HUGGINGFACE_API_KEY=*************************************

REM Set Groq API key
set GROQ_API_KEY=********************************************************

REM Navigate to backend directory
cd backend

echo.
echo ========================================
echo  MULTIMODAL RAG AGENT - VECTOR ENABLED
echo ========================================
echo.
echo ✅ ChromaDB: Running on port 8000
echo ✅ Groq AI: ChatGPT-4 Level Responses  
echo ✅ Hugging Face: Free Vector Embeddings
echo ✅ PostgreSQL: Database on port 5433
echo.
echo Starting backend server...
echo.

REM Start the Spring Boot application
./mvnw.cmd spring-boot:run

pause
