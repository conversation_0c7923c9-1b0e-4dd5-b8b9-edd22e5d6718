package com.multimodal.ragagent.service;

import com.theokanning.openai.embedding.Embedding;
import com.theokanning.openai.embedding.EmbeddingRequest;
import com.theokanning.openai.service.OpenAiService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.time.Duration;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class EmbeddingService {

    private static final Logger logger = LoggerFactory.getLogger(EmbeddingService.class);

    @Autowired
    private LocalEmbeddingService localEmbeddingService;

    @Autowired
    private HuggingFaceEmbeddingService huggingFaceEmbeddingService;

    @Value("${openai.api.key:}")
    private String openaiApiKey;

    @Value("${openai.embedding.model:text-embedding-ada-002}")
    private String openaiEmbeddingModel;

    @Value("${embedding.provider:local}")
    private String embeddingProvider;

    private OpenAiService openAiService;
    
    private OpenAiService getOpenAiService() {
        if (openAiService == null) {
            if (openaiApiKey == null || openaiApiKey.equals("your-openai-api-key-here")) {
                throw new RuntimeException("OpenAI API key not configured. Please set OPENAI_API_KEY environment variable.");
            }
            openAiService = new OpenAiService(openaiApiKey, Duration.ofSeconds(30));
        }
        return openAiService;
    }
    
    public List<Double> generateEmbedding(String text) {
        // Try Hugging Face first (free and high quality)
        if (huggingFaceEmbeddingService.isConfigured()) {
            return huggingFaceEmbeddingService.generateEmbedding(text);
        }

        // Try local embedding service (free)
        if ("local".equals(embeddingProvider) || !isOpenAIConfigured()) {
            return localEmbeddingService.generateEmbedding(text);
        }

        // Fallback to OpenAI if configured
        try {
            logger.debug("Generating embedding for text of length: {}", text.length());

            EmbeddingRequest request = EmbeddingRequest.builder()
                    .model(openaiEmbeddingModel)
                    .input(List.of(text))
                    .build();

            List<Embedding> embeddings = getOpenAiService().createEmbeddings(request).getData();

            if (embeddings.isEmpty()) {
                throw new RuntimeException("No embeddings returned from OpenAI");
            }

            List<Double> embedding = embeddings.get(0).getEmbedding();
            logger.debug("Generated embedding with {} dimensions", embedding.size());

            return embedding;

        } catch (Exception e) {
            logger.error("Failed to generate embedding: {}", e.getMessage());
            throw new RuntimeException("Failed to generate embedding", e);
        }
    }
    
    public List<List<Double>> generateEmbeddings(List<String> texts) {
        // Try Hugging Face first (free and high quality)
        if (huggingFaceEmbeddingService.isConfigured()) {
            return huggingFaceEmbeddingService.generateEmbeddings(texts);
        }

        // Try local embedding service (free)
        if ("local".equals(embeddingProvider) || !isOpenAIConfigured()) {
            return localEmbeddingService.generateEmbeddings(texts);
        }

        // Fallback to OpenAI if configured
        try {
            logger.debug("Generating embeddings for {} texts", texts.size());

            EmbeddingRequest request = EmbeddingRequest.builder()
                    .model(openaiEmbeddingModel)
                    .input(texts)
                    .build();

            List<Embedding> embeddings = getOpenAiService().createEmbeddings(request).getData();

            if (embeddings.size() != texts.size()) {
                throw new RuntimeException("Mismatch between input texts and returned embeddings");
            }

            List<List<Double>> result = embeddings.stream()
                    .map(Embedding::getEmbedding)
                    .collect(Collectors.toList());

            logger.debug("Generated {} embeddings with {} dimensions each",
                        result.size(), result.get(0).size());

            return result;

        } catch (Exception e) {
            logger.error("Failed to generate embeddings with OpenAI, falling back to local: {}", e.getMessage());
            return localEmbeddingService.generateEmbeddings(texts);
        }
    }
    
    public boolean isConfigured() {
        return huggingFaceEmbeddingService.isConfigured() ||
               localEmbeddingService.isConfigured() ||
               isOpenAIConfigured();
    }

    private boolean isOpenAIConfigured() {
        return openaiApiKey != null && !openaiApiKey.isEmpty() && !openaiApiKey.equals("your-openai-api-key-here");
    }

    public String getEmbeddingModel() {
        if (huggingFaceEmbeddingService.isConfigured()) {
            return huggingFaceEmbeddingService.getEmbeddingModel();
        }
        if ("local".equals(embeddingProvider) || !isOpenAIConfigured()) {
            return localEmbeddingService.getEmbeddingModel();
        }
        return openaiEmbeddingModel;
    }

    public int getEmbeddingDimensions() {
        if ("local".equals(embeddingProvider) || !isOpenAIConfigured()) {
            return localEmbeddingService.getEmbeddingDimensions();
        }

        // OpenAI embedding dimensions
        if ("text-embedding-ada-002".equals(openaiEmbeddingModel)) {
            return 1536;
        }
        if ("text-embedding-3-small".equals(openaiEmbeddingModel)) {
            return 1536;
        }
        if ("text-embedding-3-large".equals(openaiEmbeddingModel)) {
            return 3072;
        }
        // Default to ada-002 dimensions
        return 1536;
    }

    public String getServiceStatus() {
        if ("local".equals(embeddingProvider) || !isOpenAIConfigured()) {
            return localEmbeddingService.getServiceStatus();
        }
        return "Using OpenAI embeddings";
    }
}
