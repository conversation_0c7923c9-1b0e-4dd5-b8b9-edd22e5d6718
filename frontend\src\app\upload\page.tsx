'use client';

import React from 'react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import Layout from '@/components/Layout';
import FileUpload from '@/components/FileUpload';
import NotesGeneration from '@/components/NotesGeneration';
import { useDocumentsStore } from '@/store/documentsStore';
import { useNotesStore } from '@/store/notesStore';
import { toast } from 'sonner';
import { FileText, Upload, Clock, CheckCircle, AlertCircle, Trash2 } from 'lucide-react';

export default function UploadPage() {
  const { documents, deleteDocument } = useDocumentsStore();
  const { addNote } = useNotesStore();

  const handleDeleteDocument = async (id: number, filename: string) => {
    if (window.confirm(`Are you sure you want to delete "${filename}"?`)) {
      try {
        await deleteDocument(id);
        toast.success('Document deleted successfully');
      } catch (error) {
        toast.error('Failed to delete document');
      }
    }
  };

  const handleNoteGenerated = (note: any) => {
    addNote(note);
    toast.success(`${note.title} generated successfully!`);
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'PROCESSING':
        return <Clock className="h-4 w-4 text-yellow-500" />;
      case 'FAILED':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      default:
        return <Clock className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'COMPLETED':
        return 'bg-green-100 text-green-800';
      case 'PROCESSING':
        return 'bg-yellow-100 text-yellow-800';
      case 'FAILED':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  };

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit',
    });
  };

  return (
    <Layout>
      <div className="space-y-6">
        {/* Header */}
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Upload Documents</h1>
          <p className="text-gray-600">
            Upload your documents to start generating smart notes and chatting with your content
          </p>
        </div>

        <Tabs defaultValue="upload" className="space-y-6">
          <TabsList>
            <TabsTrigger value="upload">Upload Files</TabsTrigger>
            <TabsTrigger value="recent">Recent Uploads</TabsTrigger>
            <TabsTrigger value="notes">Generate Notes</TabsTrigger>
          </TabsList>

          <TabsContent value="upload" className="space-y-6">
            {/* Upload Instructions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Upload className="h-5 w-5 mr-2" />
                  Supported File Types
                </CardTitle>
                <CardDescription>
                  Our system can process the following document types
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-red-500" />
                    <div>
                      <p className="font-medium">PDF Documents</p>
                      <p className="text-sm text-gray-500">Text, images, tables</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-blue-500" />
                    <div>
                      <p className="font-medium">Word Documents</p>
                      <p className="text-sm text-gray-500">DOC, DOCX files</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-orange-500" />
                    <div>
                      <p className="font-medium">PowerPoint</p>
                      <p className="text-sm text-gray-500">PPT, PPTX files</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-green-500" />
                    <div>
                      <p className="font-medium">CSV Files</p>
                      <p className="text-sm text-gray-500">Structured data</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-gray-500" />
                    <div>
                      <p className="font-medium">Text Files</p>
                      <p className="text-sm text-gray-500">Plain text content</p>
                    </div>
                  </div>
                  <div className="flex items-center space-x-3 p-3 bg-gray-50 rounded-lg">
                    <FileText className="h-8 w-8 text-purple-500" />
                    <div>
                      <p className="font-medium">Handwritten Notes</p>
                      <p className="text-sm text-gray-500">OCR processing</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* File Upload Component */}
            <FileUpload />
          </TabsContent>

          <TabsContent value="recent" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Recent Uploads</CardTitle>
                <CardDescription>
                  Track the processing status of your uploaded documents
                </CardDescription>
              </CardHeader>
              <CardContent>
                {documents.length === 0 ? (
                  <div className="text-center py-8">
                    <Upload className="mx-auto h-12 w-12 text-gray-400" />
                    <h3 className="mt-2 text-sm font-medium text-gray-900">No documents uploaded</h3>
                    <p className="mt-1 text-sm text-gray-500">
                      Upload your first document to get started.
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {documents.map((document) => (
                      <div
                        key={document.id}
                        className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50"
                      >
                        <div className="flex items-center space-x-4">
                          <FileText className="h-8 w-8 text-blue-500" />
                          <div>
                            <p className="font-medium text-gray-900">
                              {document.originalFilename}
                            </p>
                            <div className="flex items-center space-x-4 text-sm text-gray-500">
                              <span>{formatFileSize(document.fileSize)}</span>
                              <span>{document.fileType}</span>
                              <span>{formatDate(document.createdAt)}</span>
                            </div>
                          </div>
                        </div>
                        <div className="flex items-center space-x-3">
                          <div className="flex items-center space-x-2">
                            {getStatusIcon(document.processingStatus)}
                            <span
                              className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(
                                document.processingStatus
                              )}`}
                            >
                              {document.processingStatus}
                            </span>
                          </div>
                          {document.processingStatus === 'COMPLETED' && (
                            <Button variant="outline" size="sm">
                              Generate Notes
                            </Button>
                          )}
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeleteDocument(document.id, document.originalFilename)}
                            className="text-red-600 hover:text-red-700 hover:bg-red-50"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="notes">
            <div className="space-y-6">
              {documents.filter(doc => doc.processingStatus === 'COMPLETED').length === 0 ? (
                <Card>
                  <CardContent className="flex items-center justify-center py-12 text-muted-foreground">
                    <div className="text-center">
                      <FileText className="h-12 w-12 mx-auto mb-4 opacity-50" />
                      <h3 className="font-medium mb-2">No Documents Ready</h3>
                      <p className="text-sm">
                        Upload and process documents first to generate notes
                      </p>
                    </div>
                  </CardContent>
                </Card>
              ) : (
                <div className="grid gap-6">
                  {documents
                    .filter(doc => doc.processingStatus === 'COMPLETED')
                    .map((document) => (
                      <Card key={document.id}>
                        <CardHeader>
                          <CardTitle className="flex items-center gap-2">
                            <FileText className="h-5 w-5" />
                            {document.originalFilename}
                            <Badge variant="secondary" className="ml-auto">
                              Ready for Notes
                            </Badge>
                          </CardTitle>
                        </CardHeader>
                        <CardContent>
                          <NotesGeneration
                            documentId={document.id}
                            documentName={document.originalFilename}
                            onNoteGenerated={handleNoteGenerated}
                          />
                        </CardContent>
                      </Card>
                    ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </Layout>
  );
}
