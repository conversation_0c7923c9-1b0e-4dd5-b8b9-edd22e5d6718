package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import com.multimodal.ragagent.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class NotesGenerationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotesGenerationService.class);
    
    @Autowired
    private GroqService groqService;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private NoteRepository noteRepository;
    
    public enum NoteType {
        DETAILED("detailed", "Detailed Notes", "Comprehensive notes with all important details"),
        QUICK("quick", "Quick Revision Notes", "Concise notes for quick review"),
        MEDIUM("medium", "Medium Level Notes", "Balanced notes with key concepts");
        
        private final String code;
        private final String displayName;
        private final String description;
        
        NoteType(String code, String displayName, String description) {
            this.code = code;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        
        public static NoteType fromCode(String code) {
            for (NoteType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MEDIUM; // default
        }
    }
    
    public Note generateNotes(Long documentId, String noteType, User user) {
        try {
            // Verify user has access to the document
            Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
            if (documentOpt.isEmpty()) {
                throw new RuntimeException("Document not found or access denied");
            }
            
            Document document = documentOpt.get();
            NoteType type = NoteType.fromCode(noteType);
            
            logger.info("Generating {} notes for document: {} by user: {}", 
                       type.getDisplayName(), document.getOriginalFilename(), user.getUsername());
            
            // Get document content
            String documentContent = getDocumentContent(document);
            
            if (documentContent == null || documentContent.trim().isEmpty()) {
                throw new RuntimeException("Document content is empty or not processed yet");
            }
            
            // Generate notes using Groq
            String notesContent = generateNotesContent(documentContent, type);
            
            // Create and save the note
            Note note = new Note();
            note.setTitle(generateNoteTitle(document, type));
            note.setContent(notesContent);
            note.setUser(user);
            note.setCreatedAt(LocalDateTime.now());
            note.setUpdatedAt(LocalDateTime.now());
            
            Note savedNote = noteRepository.save(note);
            
            logger.info("Successfully generated {} notes (ID: {}) for document: {}", 
                       type.getDisplayName(), savedNote.getId(), document.getOriginalFilename());
            
            return savedNote;
            
        } catch (Exception e) {
            logger.error("Failed to generate notes for document {}: {}", documentId, e.getMessage());
            throw new RuntimeException("Failed to generate notes: " + e.getMessage());
        }
    }
    
    private String getDocumentContent(Document document) {
        // For now, we'll use the extracted text content
        // In the future, this could be enhanced to use chunks or specific sections
        return document.getExtractedText();
    }
    
    private String generateNotesContent(String documentContent, NoteType noteType) {
        if (!groqService.isConfigured()) {
            return generateFallbackNotes(documentContent, noteType);
        }
        
        String prompt = buildNotesPrompt(noteType);
        
        try {
            String generatedNotes = groqService.generateNotes(documentContent, noteType.getCode());
            
            // Add metadata header
            StringBuilder finalNotes = new StringBuilder();
            finalNotes.append("# ").append(noteType.getDisplayName()).append("\n\n");
            finalNotes.append("*Generated on: ").append(LocalDateTime.now().toString()).append("*\n");
            finalNotes.append("*Type: ").append(noteType.getDescription()).append("*\n\n");
            finalNotes.append("---\n\n");
            finalNotes.append(generatedNotes);
            
            return finalNotes.toString();
            
        } catch (Exception e) {
            logger.warn("Failed to generate AI notes, using fallback: {}", e.getMessage());
            return generateFallbackNotes(documentContent, noteType);
        }
    }
    
    private String buildNotesPrompt(NoteType noteType) {
        switch (noteType) {
            case DETAILED:
                return "Create comprehensive, detailed notes from the following document content. " +
                       "Include all important points, explanations, examples, and key details. " +
                       "Organize the notes with clear headings, subheadings, and bullet points. " +
                       "Make sure to capture nuances and context.";
                       
            case QUICK:
                return "Create quick revision notes from the following document content. " +
                       "Focus on key points, important facts, and essential information only. " +
                       "Use bullet points and keep it very concise for quick review. " +
                       "Prioritize the most important concepts.";
                       
            case MEDIUM:
                return "Create medium-level notes from the following document content. " +
                       "Include important concepts, main points, and supporting details. " +
                       "Balance comprehensiveness with readability. " +
                       "Use clear structure with headings and bullet points.";
                       
            default:
                return "Create well-organized notes from the following document content.";
        }
    }
    
    private String generateFallbackNotes(String documentContent, NoteType noteType) {
        StringBuilder notes = new StringBuilder();
        
        notes.append("# ").append(noteType.getDisplayName()).append("\n\n");
        notes.append("*Generated on: ").append(LocalDateTime.now().toString()).append("*\n");
        notes.append("*Type: ").append(noteType.getDescription()).append("*\n\n");
        notes.append("---\n\n");
        
        switch (noteType) {
            case DETAILED:
                notes.append("## Document Content\n\n");
                notes.append(documentContent);
                notes.append("\n\n## Key Points\n\n");
                notes.append("- Please review the full document content above\n");
                notes.append("- For AI-generated detailed notes, configure your Groq API key\n");
                break;
                
            case QUICK:
                String[] sentences = documentContent.split("\\. ");
                notes.append("## Quick Points\n\n");
                int count = 0;
                for (String sentence : sentences) {
                    if (count >= 10) break; // Limit for quick notes
                    if (sentence.trim().length() > 20) {
                        notes.append("- ").append(sentence.trim()).append("\n");
                        count++;
                    }
                }
                notes.append("\n*For AI-generated quick notes, configure your Groq API key*\n");
                break;
                
            case MEDIUM:
                notes.append("## Summary\n\n");
                String summary = documentContent.length() > 1000 ? 
                    documentContent.substring(0, 1000) + "..." : documentContent;
                notes.append(summary);
                notes.append("\n\n## Notes\n\n");
                notes.append("- This is a basic summary of the document\n");
                notes.append("- For AI-generated medium-level notes, configure your Groq API key\n");
                break;
        }
        
        return notes.toString();
    }
    
    private String generateNoteTitle(Document document, NoteType noteType) {
        String baseTitle = document.getOriginalFilename();
        if (baseTitle.contains(".")) {
            baseTitle = baseTitle.substring(0, baseTitle.lastIndexOf("."));
        }
        
        return String.format("%s - %s", baseTitle, noteType.getDisplayName());
    }
    
    public boolean canGenerateNotes(Long documentId, User user) {
        Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
        if (documentOpt.isEmpty()) {
            return false;
        }
        
        Document document = documentOpt.get();
        return document.getProcessingStatus() == Document.ProcessingStatus.COMPLETED &&
               document.getExtractedText() != null && 
               !document.getExtractedText().trim().isEmpty();
    }
    
    public String getServiceStatus() {
        return groqService.getServiceStatus();
    }
    
    public boolean isAINotesAvailable() {
        return groqService.isConfigured();
    }
}
