package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.Document;
import com.multimodal.ragagent.entity.Note;
import com.multimodal.ragagent.entity.User;
import com.multimodal.ragagent.repository.DocumentRepository;
import com.multimodal.ragagent.repository.NoteRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.time.LocalDateTime;
import java.util.Optional;

@Service
public class NotesGenerationService {
    
    private static final Logger logger = LoggerFactory.getLogger(NotesGenerationService.class);
    
    @Autowired
    private GroqService groqService;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private NoteRepository noteRepository;
    
    public enum NoteType {
        DETAILED("detailed", "Detailed Notes", "Comprehensive notes with all important details"),
        QUICK("quick", "Quick Revision Notes", "Concise notes for quick review"),
        MEDIUM("medium", "Medium Level Notes", "Balanced notes with key concepts"),
        SUMMARY("summary", "Executive Summary", "High-level overview and key takeaways"),
        FLASHCARDS("flashcards", "Flashcards", "Question-answer pairs for active recall"),
        QNA("qna", "Q&A Study Guide", "Questions and answers for exam preparation"),
        OUTLINE("outline", "Structured Outline", "Hierarchical outline of main topics"),
        MINDMAP("mindmap", "Mind Map", "Visual concept map with relationships"),
        STUDY_GUIDE("study_guide", "Study Guide", "Comprehensive study material with exercises"),
        KEY_POINTS("key_points", "Key Points", "Essential facts and important highlights");
        
        private final String code;
        private final String displayName;
        private final String description;
        
        NoteType(String code, String displayName, String description) {
            this.code = code;
            this.displayName = displayName;
            this.description = description;
        }
        
        public String getCode() { return code; }
        public String getDisplayName() { return displayName; }
        public String getDescription() { return description; }
        
        public static NoteType fromCode(String code) {
            for (NoteType type : values()) {
                if (type.code.equals(code)) {
                    return type;
                }
            }
            return MEDIUM; // default
        }
    }
    
    public Note generateNotes(Long documentId, String noteType, User user) {
        try {
            // Verify user has access to the document
            Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
            if (documentOpt.isEmpty()) {
                throw new RuntimeException("Document not found or access denied");
            }
            
            Document document = documentOpt.get();
            NoteType type = NoteType.fromCode(noteType);
            
            logger.info("Generating {} notes for document: {} by user: {}", 
                       type.getDisplayName(), document.getOriginalFilename(), user.getUsername());
            
            // Get document content
            String documentContent = getDocumentContent(document);
            
            if (documentContent == null || documentContent.trim().isEmpty()) {
                throw new RuntimeException("Document content is empty or not processed yet");
            }
            
            // Generate notes using Groq
            String notesContent = generateNotesContent(documentContent, type);
            
            // Create and save the note
            Note note = new Note();
            note.setTitle(generateNoteTitle(document, type));
            note.setContent(notesContent);
            note.setUser(user);
            note.setCreatedAt(LocalDateTime.now());
            note.setUpdatedAt(LocalDateTime.now());
            
            Note savedNote = noteRepository.save(note);
            
            logger.info("Successfully generated {} notes (ID: {}) for document: {}", 
                       type.getDisplayName(), savedNote.getId(), document.getOriginalFilename());
            
            return savedNote;
            
        } catch (Exception e) {
            logger.error("Failed to generate notes for document {}: {}", documentId, e.getMessage());
            throw new RuntimeException("Failed to generate notes: " + e.getMessage());
        }
    }
    
    private String getDocumentContent(Document document) {
        // For now, we'll use the extracted text content
        // In the future, this could be enhanced to use chunks or specific sections
        return document.getExtractedText();
    }
    
    private String generateNotesContent(String documentContent, NoteType noteType) {
        if (!groqService.isConfigured()) {
            return generateFallbackNotes(documentContent, noteType);
        }
        
        String prompt = buildNotesPrompt(noteType);
        
        try {
            String generatedNotes = groqService.generateNotes(documentContent, noteType.getCode());
            
            // Add metadata header
            StringBuilder finalNotes = new StringBuilder();
            finalNotes.append("# ").append(noteType.getDisplayName()).append("\n\n");
            finalNotes.append("*Generated on: ").append(LocalDateTime.now().toString()).append("*\n");
            finalNotes.append("*Type: ").append(noteType.getDescription()).append("*\n\n");
            finalNotes.append("---\n\n");
            finalNotes.append(generatedNotes);
            
            return finalNotes.toString();
            
        } catch (Exception e) {
            logger.warn("Failed to generate AI notes, using fallback: {}", e.getMessage());
            return generateFallbackNotes(documentContent, noteType);
        }
    }
    
    private String buildNotesPrompt(NoteType noteType) {
        switch (noteType) {
            case DETAILED:
                return "Create comprehensive, detailed notes from the following document content. " +
                       "Include all important points, explanations, examples, and key details. " +
                       "Organize the notes with clear headings, subheadings, and bullet points. " +
                       "Make sure to capture nuances and context.";

            case QUICK:
                return "Create quick revision notes from the following document content. " +
                       "Focus on key points, important facts, and essential information only. " +
                       "Use bullet points and keep it very concise for quick review. " +
                       "Prioritize the most important concepts.";

            case MEDIUM:
                return "Create medium-level notes from the following document content. " +
                       "Include important concepts, main points, and supporting details. " +
                       "Balance comprehensiveness with readability. " +
                       "Use clear structure with headings and bullet points.";

            case SUMMARY:
                return "Create an executive summary from the following document content. " +
                       "Focus on the main purpose, key findings, conclusions, and actionable insights. " +
                       "Keep it concise but comprehensive. Use professional language and clear structure.";

            case FLASHCARDS:
                return "Create flashcards from the following document content. " +
                       "Format as 'Q: [Question]' followed by 'A: [Answer]' pairs. " +
                       "Focus on key concepts, definitions, facts, and important details. " +
                       "Create 10-20 flashcards covering the most important information.";

            case QNA:
                return "Create a Q&A study guide from the following document content. " +
                       "Generate comprehensive questions and detailed answers covering all major topics. " +
                       "Include different types of questions: factual, conceptual, and analytical. " +
                       "Format clearly with numbered questions and detailed answers.";

            case OUTLINE:
                return "Create a structured outline from the following document content. " +
                       "Use hierarchical numbering (1., 1.1, 1.1.1) and organize information logically. " +
                       "Include main topics, subtopics, and key points. " +
                       "Make it easy to follow and comprehensive.";

            case MINDMAP:
                return "Create a mind map structure from the following document content. " +
                       "Present as a text-based mind map with central topic and branching subtopics. " +
                       "Use indentation and symbols to show relationships. " +
                       "Focus on connections between concepts and ideas.";

            case STUDY_GUIDE:
                return "Create a comprehensive study guide from the following document content. " +
                       "Include key concepts, important facts, practice questions, and study tips. " +
                       "Organize by topics with clear sections. Add memory aids and mnemonics where helpful. " +
                       "Make it practical for exam preparation.";

            case KEY_POINTS:
                return "Extract and organize the key points from the following document content. " +
                       "Focus on the most important facts, concepts, and takeaways. " +
                       "Use bullet points and clear categorization. " +
                       "Prioritize information by importance and relevance.";

            default:
                return "Create well-organized notes from the following document content.";
        }
    }
    
    private String generateFallbackNotes(String documentContent, NoteType noteType) {
        StringBuilder notes = new StringBuilder();
        
        notes.append("# ").append(noteType.getDisplayName()).append("\n\n");
        notes.append("*Generated on: ").append(LocalDateTime.now().toString()).append("*\n");
        notes.append("*Type: ").append(noteType.getDescription()).append("*\n\n");
        notes.append("---\n\n");
        
        switch (noteType) {
            case DETAILED:
                notes.append("## Document Content\n\n");
                notes.append(documentContent);
                notes.append("\n\n## Key Points\n\n");
                notes.append("- Please review the full document content above\n");
                notes.append("- For AI-generated detailed notes, configure your Groq API key\n");
                break;

            case QUICK:
                String[] sentences = documentContent.split("\\. ");
                notes.append("## Quick Points\n\n");
                int count = 0;
                for (String sentence : sentences) {
                    if (count >= 10) break; // Limit for quick notes
                    if (sentence.trim().length() > 20) {
                        notes.append("- ").append(sentence.trim()).append("\n");
                        count++;
                    }
                }
                notes.append("\n*For AI-generated quick notes, configure your Groq API key*\n");
                break;

            case MEDIUM:
                notes.append("## Summary\n\n");
                String summary = documentContent.length() > 1000 ?
                    documentContent.substring(0, 1000) + "..." : documentContent;
                notes.append(summary);
                notes.append("\n\n## Notes\n\n");
                notes.append("- This is a basic summary of the document\n");
                notes.append("- For AI-generated medium-level notes, configure your Groq API key\n");
                break;

            case SUMMARY:
                notes.append("## Executive Summary\n\n");
                String execSummary = documentContent.length() > 500 ?
                    documentContent.substring(0, 500) + "..." : documentContent;
                notes.append(execSummary);
                notes.append("\n\n## Key Takeaways\n\n");
                notes.append("- Main points from the document\n");
                notes.append("- For AI-generated executive summary, configure your Groq API key\n");
                break;

            case FLASHCARDS:
                notes.append("## Sample Flashcards\n\n");
                notes.append("**Q:** What is the main topic of this document?\n");
                notes.append("**A:** ").append(documentContent.substring(0, Math.min(100, documentContent.length()))).append("...\n\n");
                notes.append("**Q:** Key concept from the document?\n");
                notes.append("**A:** Please review the document for key concepts\n\n");
                notes.append("*For AI-generated flashcards, configure your Groq API key*\n");
                break;

            case QNA:
                notes.append("## Sample Q&A\n\n");
                notes.append("**Question 1:** What is the main purpose of this document?\n");
                notes.append("**Answer:** The document covers various topics. Please review for specific details.\n\n");
                notes.append("**Question 2:** What are the key points discussed?\n");
                notes.append("**Answer:** Key points can be found throughout the document.\n\n");
                notes.append("*For AI-generated Q&A study guide, configure your Groq API key*\n");
                break;

            case OUTLINE:
                notes.append("## Document Outline\n\n");
                notes.append("1. Introduction\n");
                notes.append("2. Main Content\n");
                notes.append("   2.1 Key Topics\n");
                notes.append("   2.2 Supporting Details\n");
                notes.append("3. Conclusion\n\n");
                notes.append("*For AI-generated structured outline, configure your Groq API key*\n");
                break;

            case MINDMAP:
                notes.append("## Mind Map Structure\n\n");
                notes.append("```\n");
                notes.append("Document Topic\n");
                notes.append("├── Main Concept 1\n");
                notes.append("│   ├── Detail A\n");
                notes.append("│   └── Detail B\n");
                notes.append("├── Main Concept 2\n");
                notes.append("│   ├── Detail C\n");
                notes.append("│   └── Detail D\n");
                notes.append("└── Conclusion\n");
                notes.append("```\n\n");
                notes.append("*For AI-generated mind map, configure your Groq API key*\n");
                break;

            case STUDY_GUIDE:
                notes.append("## Study Guide\n\n");
                notes.append("### Key Concepts\n");
                notes.append("- Review the main topics in the document\n\n");
                notes.append("### Practice Questions\n");
                notes.append("1. What is the main theme?\n");
                notes.append("2. What are the supporting arguments?\n\n");
                notes.append("### Study Tips\n");
                notes.append("- Read the document multiple times\n");
                notes.append("- Take notes on key points\n\n");
                notes.append("*For AI-generated comprehensive study guide, configure your Groq API key*\n");
                break;

            case KEY_POINTS:
                notes.append("## Key Points\n\n");
                String[] keyParagraphs = documentContent.split("\n\n");
                int pointCount = 0;
                for (String paragraph : keyParagraphs) {
                    if (pointCount >= 8) break; // Limit key points
                    if (paragraph.trim().length() > 50) {
                        notes.append("• ").append(paragraph.trim().substring(0, Math.min(150, paragraph.trim().length())));
                        if (paragraph.trim().length() > 150) notes.append("...");
                        notes.append("\n\n");
                        pointCount++;
                    }
                }
                notes.append("*For AI-generated key points extraction, configure your Groq API key*\n");
                break;
        }
        
        return notes.toString();
    }
    
    private String generateNoteTitle(Document document, NoteType noteType) {
        String baseTitle = document.getOriginalFilename();
        if (baseTitle.contains(".")) {
            baseTitle = baseTitle.substring(0, baseTitle.lastIndexOf("."));
        }
        
        return String.format("%s - %s", baseTitle, noteType.getDisplayName());
    }
    
    public boolean canGenerateNotes(Long documentId, User user) {
        Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
        if (documentOpt.isEmpty()) {
            return false;
        }
        
        Document document = documentOpt.get();
        return document.getProcessingStatus() == Document.ProcessingStatus.COMPLETED &&
               document.getExtractedText() != null && 
               !document.getExtractedText().trim().isEmpty();
    }
    
    public String getServiceStatus() {
        return groqService.getServiceStatus();
    }
    
    public boolean isAINotesAvailable() {
        return groqService.isConfigured();
    }
}
