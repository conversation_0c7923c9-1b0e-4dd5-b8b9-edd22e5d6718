# PowerShell script to start the backend with API keys
# IMPORTANT: Replace the placeholder values below with your actual API keys

Write-Host "=== Multimodal RAG Agent Startup Script ===" -ForegroundColor Cyan
Write-Host "Please ensure you have replaced the placeholder API keys below with your actual keys!" -ForegroundColor Yellow
Write-Host ""

# Set environment variables for API keys
# REPLACE THESE WITH YOUR ACTUAL API KEYS:
$env:GROQ_API_KEY = "********************************************************"
$env:HUGGINGFACE_API_KEY = "hf_YOUR_ACTUAL_HUGGINGFACE_API_KEY_HERE"

# Validate API keys are not placeholders
if ($env:GROQ_API_KEY -eq "gsk_YOUR_ACTUAL_GROQ_API_KEY_HERE" -or $env:HUGGINGFACE_API_KEY -eq "hf_YOUR_ACTUAL_HUGGINGFACE_API_KEY_HERE") {
    Write-Host "ERROR: Please replace the placeholder API keys with your actual keys!" -ForegroundColor Red
    Write-Host "Edit this file (start-with-apis.ps1) and replace:" -ForegroundColor Red
    Write-Host "  - gsk_YOUR_ACTUAL_GROQ_API_KEY_HERE" -ForegroundColor Red
    Write-Host "  - hf_YOUR_ACTUAL_HUGGINGFACE_API_KEY_HERE" -ForegroundColor Red
    Write-Host ""
    Write-Host "Press any key to continue anyway (will use fallback responses)..." -ForegroundColor Yellow
    $null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
}

Write-Host "Starting backend with API keys configured..." -ForegroundColor Green
Write-Host "Groq API Key: $($env:GROQ_API_KEY.Substring(0, [Math]::Min(10, $env:GROQ_API_KEY.Length)))..." -ForegroundColor Yellow
Write-Host "HuggingFace API Key: $($env:HUGGINGFACE_API_KEY.Substring(0, [Math]::Min(10, $env:HUGGINGFACE_API_KEY.Length)))..." -ForegroundColor Yellow
Write-Host ""

# Start the Spring Boot application
.\mvnw.cmd spring-boot:run
