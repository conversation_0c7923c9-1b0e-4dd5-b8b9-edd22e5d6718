'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  CheckCircle, 
  AlertCircle, 
  XCircle, 
  Loader2, 
  RefreshCw,
  Database,
  Zap,
  MessageSquare,
  FileText,
  Settings
} from 'lucide-react';
import { ragAPI, notesAPI, chatAPI } from '@/lib/api';

interface ServiceStatus {
  name: string;
  status: 'healthy' | 'warning' | 'error' | 'unknown';
  message: string;
  details?: string;
  icon: React.ReactNode;
}

export default function SystemStatus() {
  const [services, setServices] = useState<ServiceStatus[]>([]);
  const [loading, setLoading] = useState(true);
  const [lastUpdated, setLastUpdated] = useState<Date | null>(null);

  useEffect(() => {
    checkAllServices();
    
    // Auto-refresh every 30 seconds
    const interval = setInterval(checkAllServices, 30000);
    return () => clearInterval(interval);
  }, []);

  const checkAllServices = async () => {
    setLoading(true);
    
    const serviceChecks = [
      checkRAGService(),
      checkNotesService(),
      checkChatService(),
    ];

    try {
      const results = await Promise.allSettled(serviceChecks);
      const serviceStatuses: ServiceStatus[] = [];

      results.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          serviceStatuses.push(result.value);
        } else {
          // Handle failed service checks
          const serviceNames = ['RAG Service', 'Notes Service', 'Chat Service'];
          serviceStatuses.push({
            name: serviceNames[index],
            status: 'error',
            message: 'Service check failed',
            details: result.reason?.message || 'Unknown error',
            icon: getServiceIcon(serviceNames[index]),
          });
        }
      });

      setServices(serviceStatuses);
      setLastUpdated(new Date());
    } catch (error) {
      console.error('Failed to check services:', error);
    } finally {
      setLoading(false);
    }
  };

  const checkRAGService = async (): Promise<ServiceStatus> => {
    try {
      const response = await ragAPI.getStatus();
      const data = response.data;
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy';
      let message = 'All systems operational';
      let details = '';

      if (!data.groqConfigured) {
        status = 'warning';
        message = 'Using intelligent fallback';
        details = 'Groq API not configured - configure for ChatGPT-4 level responses';
      } else if (!data.available) {
        status = 'warning';
        message = 'ChatGPT-4 level AI active';
        details = 'Vector DB offline but Groq AI provides full document analysis';
      } else {
        status = 'healthy';
        message = 'ChatGPT-4 level AI with vector enhancement';
        details = `Vector DB: ${data.vectorDatabase}, Groq: ${data.groqService}`;
      }

      return {
        name: 'RAG Service',
        status,
        message,
        details,
        icon: <Database className="h-4 w-4" />,
      };
    } catch (error) {
      return {
        name: 'RAG Service',
        status: 'error',
        message: 'Service unavailable',
        details: 'Failed to connect to RAG service',
        icon: <Database className="h-4 w-4" />,
      };
    }
  };

  const checkNotesService = async (): Promise<ServiceStatus> => {
    try {
      const response = await notesAPI.getStatus();
      const data = response.data;
      
      let status: 'healthy' | 'warning' | 'error' = 'healthy';
      let message = 'AI-powered notes ready';
      let details = data.serviceStatus || 'Groq AI integration active';

      if (!data.aiAvailable) {
        status = 'warning';
        message = 'Using intelligent fallback';
        details = 'AI service not available - enhanced notes still available';
      }

      return {
        name: 'Notes Service',
        status,
        message,
        details,
        icon: <FileText className="h-4 w-4" />,
      };
    } catch (error) {
      return {
        name: 'Notes Service',
        status: 'error',
        message: 'Service unavailable',
        details: 'Failed to connect to notes service',
        icon: <FileText className="h-4 w-4" />,
      };
    }
  };

  const checkChatService = async (): Promise<ServiceStatus> => {
    try {
      const response = await chatAPI.getStatistics();
      
      return {
        name: 'Chat Service',
        status: 'healthy',
        message: 'ChatGPT-4 level conversations',
        details: `${response.data.totalSessions || 0} sessions • Full document integration`,
        icon: <MessageSquare className="h-4 w-4" />,
      };
    } catch (error) {
      return {
        name: 'Chat Service',
        status: 'error',
        message: 'Service unavailable',
        details: 'Failed to connect to chat service',
        icon: <MessageSquare className="h-4 w-4" />,
      };
    }
  };

  const getServiceIcon = (serviceName: string) => {
    switch (serviceName) {
      case 'RAG Service': return <Database className="h-4 w-4" />;
      case 'Notes Service': return <FileText className="h-4 w-4" />;
      case 'Chat Service': return <MessageSquare className="h-4 w-4" />;
      default: return <Settings className="h-4 w-4" />;
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'healthy': return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'warning': return <AlertCircle className="h-4 w-4 text-yellow-500" />;
      case 'error': return <XCircle className="h-4 w-4 text-red-500" />;
      default: return <AlertCircle className="h-4 w-4 text-gray-500" />;
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'healthy': return <Badge className="bg-green-100 text-green-800">Healthy</Badge>;
      case 'warning': return <Badge className="bg-yellow-100 text-yellow-800">Warning</Badge>;
      case 'error': return <Badge className="bg-red-100 text-red-800">Error</Badge>;
      default: return <Badge variant="secondary">Unknown</Badge>;
    }
  };

  const getOverallStatus = () => {
    if (services.some(s => s.status === 'error')) return 'error';
    if (services.some(s => s.status === 'warning')) return 'warning';
    if (services.every(s => s.status === 'healthy')) return 'healthy';
    return 'unknown';
  };

  const overallStatus = getOverallStatus();

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            System Status
            {getStatusBadge(overallStatus)}
          </CardTitle>
          <Button
            variant="outline"
            size="sm"
            onClick={checkAllServices}
            disabled={loading}
          >
            {loading ? (
              <Loader2 className="h-4 w-4 animate-spin" />
            ) : (
              <RefreshCw className="h-4 w-4" />
            )}
          </Button>
        </div>
        {lastUpdated && (
          <p className="text-sm text-muted-foreground">
            Last updated: {lastUpdated.toLocaleTimeString()}
          </p>
        )}
      </CardHeader>
      <CardContent className="space-y-4">
        {loading && services.length === 0 ? (
          <div className="flex items-center justify-center py-8">
            <Loader2 className="h-6 w-6 animate-spin" />
            <span className="ml-2">Checking services...</span>
          </div>
        ) : (
          services.map((service) => (
            <div
              key={service.name}
              className="flex items-center justify-between p-3 rounded-lg border"
            >
              <div className="flex items-center gap-3">
                {service.icon}
                <div>
                  <div className="font-medium">{service.name}</div>
                  <div className="text-sm text-muted-foreground">
                    {service.message}
                  </div>
                  {service.details && (
                    <div className="text-xs text-muted-foreground mt-1">
                      {service.details}
                    </div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                {getStatusIcon(service.status)}
                {getStatusBadge(service.status)}
              </div>
            </div>
          ))
        )}

        {/* System Information */}
        <div className="mt-6 p-4 bg-muted rounded-lg">
          <h4 className="font-medium mb-2">System Information</h4>
          <div className="text-sm space-y-2">
            <div className="flex items-center justify-between">
              <span>AI Engine:</span>
              <Badge variant="outline">Groq API (ChatGPT-4 Level)</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Document Processing:</span>
              <Badge variant="outline">Text + Images + Tables + OCR</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Chat Integration:</span>
              <Badge variant="outline">Full Document Context</Badge>
            </div>
            <div className="flex items-center justify-between">
              <span>Fallback System:</span>
              <Badge variant="outline">Intelligent Analysis</Badge>
            </div>
          </div>

          {overallStatus !== 'healthy' && (
            <div className="mt-4 pt-4 border-t">
              <h5 className="font-medium mb-2">Optimization Tips</h5>
              <div className="text-sm space-y-1">
                {services.some(s => s.name === 'RAG Service' && s.status === 'warning' && s.details?.includes('Vector DB')) && (
                  <p>• <strong>Optional:</strong> Start ChromaDB for vector enhancement: <code className="bg-background px-1 rounded">docker run -p 8000:8000 chromadb/chroma</code></p>
                )}
                {services.some(s => s.status === 'warning' && s.details?.includes('Groq API not configured')) && (
                  <p>• <strong>Recommended:</strong> Configure Groq API for ChatGPT-4 level responses: Set <code className="bg-background px-1 rounded">GROQ_API_KEY</code></p>
                )}
                <p>• Your system works great even without these optimizations!</p>
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
