package com.multimodal.ragagent.service;

import com.multimodal.ragagent.entity.*;
import com.multimodal.ragagent.repository.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;

@Service
@Transactional
public class ChatService {
    
    private static final Logger logger = LoggerFactory.getLogger(ChatService.class);
    
    @Autowired
    private ChatSessionRepository chatSessionRepository;
    
    @Autowired
    private ChatMessageRepository chatMessageRepository;
    
    @Autowired
    private DocumentRepository documentRepository;
    
    @Autowired
    private VectorService vectorService;
    
    @Autowired
    private GroqService groqService;
    
    public ChatSession createChatSession(Long documentId, User user) {
        try {
            // Verify user has access to the document
            Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
            if (documentOpt.isEmpty()) {
                throw new RuntimeException("Document not found or access denied");
            }
            
            Document document = documentOpt.get();
            
            // Generate session title
            String title = generateSessionTitle(document);
            
            // Create new chat session
            ChatSession chatSession = new ChatSession(title, user, document);
            ChatSession savedSession = chatSessionRepository.save(chatSession);
            
            logger.info("Created chat session {} for document {} by user {}", 
                       savedSession.getId(), document.getOriginalFilename(), user.getUsername());
            
            return savedSession;
            
        } catch (Exception e) {
            logger.error("Failed to create chat session for document {}: {}", documentId, e.getMessage());
            throw new RuntimeException("Failed to create chat session: " + e.getMessage());
        }
    }
    
    public ChatMessage sendMessage(Long sessionId, String messageContent, User user) {
        try {
            // Get chat session
            Optional<ChatSession> sessionOpt = chatSessionRepository.findByIdAndUser(sessionId, user);
            if (sessionOpt.isEmpty()) {
                throw new RuntimeException("Chat session not found or access denied");
            }
            
            ChatSession chatSession = sessionOpt.get();
            
            // Create user message
            ChatMessage userMessage = new ChatMessage(chatSession, ChatMessage.MessageRole.USER, messageContent);
            chatMessageRepository.save(userMessage);
            
            // Generate AI response
            long startTime = System.currentTimeMillis();
            String aiResponse = generateAIResponse(chatSession, messageContent);
            long responseTime = System.currentTimeMillis() - startTime;
            
            // Create AI message
            ChatMessage aiMessage = new ChatMessage(chatSession, ChatMessage.MessageRole.ASSISTANT, aiResponse);
            aiMessage.setResponseTimeMs(responseTime);
            chatMessageRepository.save(aiMessage);
            
            // Update session timestamp
            chatSession.setUpdatedAt(LocalDateTime.now());
            chatSessionRepository.save(chatSession);
            
            logger.info("Processed chat message in session {} (response time: {}ms)", sessionId, responseTime);
            
            return aiMessage;
            
        } catch (Exception e) {
            logger.error("Failed to send message in session {}: {}", sessionId, e.getMessage());
            throw new RuntimeException("Failed to send message: " + e.getMessage());
        }
    }
    
    private String generateAIResponse(ChatSession chatSession, String userMessage) {
        try {
            Document document = chatSession.getDocument();
            logger.info("Generating ChatGPT-4 level chat response for document: {}", document.getOriginalFilename());

            // Get chat history for context
            List<ChatMessage> recentMessages = getRecentMessages(chatSession, 10);
            List<Map<String, String>> chatHistory = recentMessages.stream()
                    .map(msg -> {
                        Map<String, String> historyMsg = new HashMap<>();
                        historyMsg.put("role", msg.getRole().name().toLowerCase());
                        historyMsg.put("content", msg.getContent());
                        return historyMsg;
                    })
                    .collect(Collectors.toList());

            // Try vector search first if available
            if (vectorService.isVectorDatabaseAvailable()) {
                List<DocumentChunk> relevantChunks = vectorService.searchSimilarChunks(userMessage, 8);

                // Filter chunks to only include those from the current document
                List<DocumentChunk> documentChunks = relevantChunks.stream()
                        .filter(chunk -> chunk.getDocument().getId().equals(document.getId()))
                        .collect(Collectors.toList());

                if (!documentChunks.isEmpty()) {
                    String context = buildContextFromChunks(documentChunks);
                    String response = groqService.generateChatGPTLevelChatResponse(userMessage, context, document.getOriginalFilename(), chatHistory);

                    logger.info("Generated vector-enhanced chat response for document: {}", document.getOriginalFilename());
                    return response;
                }
            }

            // Fallback: Use full document content for comprehensive chat
            String documentContent = document.getExtractedText();
            if (documentContent == null || documentContent.trim().isEmpty()) {
                return "I apologize, but this document hasn't been processed yet or contains no readable text. Please try uploading the document again.";
            }

            // Generate ChatGPT-4 level chat response using full document
            String response = groqService.generateChatGPTLevelChatResponse(userMessage, documentContent, document.getOriginalFilename(), chatHistory);

            logger.info("Generated full-document chat response for: {}", document.getOriginalFilename());
            return response;

        } catch (Exception e) {
            logger.error("Failed to generate ChatGPT-4 level chat response: {}", e.getMessage());
            return "I encountered an issue while processing your message. Please try rephrasing your question or try again in a moment.";
        }
    }
    
    private String buildContextFromChunks(List<DocumentChunk> chunks) {
        if (chunks.isEmpty()) {
            return "No specific context found in the document for this question.";
        }
        
        StringBuilder context = new StringBuilder();
        for (DocumentChunk chunk : chunks) {
            context.append(chunk.getContent()).append("\n\n");
        }
        
        return context.toString();
    }
    
    private List<ChatMessage> getRecentMessages(ChatSession chatSession, int limit) {
        List<ChatMessage> allMessages = chatMessageRepository.findByChatSessionOrderByCreatedAtAsc(chatSession);
        
        // Return the last 'limit' messages, excluding system messages
        return allMessages.stream()
                .filter(msg -> msg.getRole() != ChatMessage.MessageRole.SYSTEM)
                .skip(Math.max(0, allMessages.size() - limit))
                .collect(Collectors.toList());
    }
    
    public List<ChatSession> getUserChatSessions(User user) {
        return chatSessionRepository.findActiveSessionsByUser(user);
    }
    
    public Page<ChatSession> getUserChatSessions(User user, Pageable pageable) {
        return chatSessionRepository.findByUserAndIsActiveOrderByUpdatedAtDesc(user, true, pageable);
    }
    
    public List<ChatSession> getDocumentChatSessions(Long documentId, User user) {
        Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
        if (documentOpt.isEmpty()) {
            throw new RuntimeException("Document not found or access denied");
        }
        
        return chatSessionRepository.findActiveSessionsByDocumentAndUser(documentOpt.get(), user);
    }
    
    public List<ChatMessage> getChatMessages(Long sessionId, User user) {
        Optional<ChatSession> sessionOpt = chatSessionRepository.findByIdAndUser(sessionId, user);
        if (sessionOpt.isEmpty()) {
            throw new RuntimeException("Chat session not found or access denied");
        }
        
        return chatMessageRepository.findByChatSessionOrderByCreatedAtAsc(sessionOpt.get());
    }
    
    public ChatSession getChatSession(Long sessionId, User user) {
        return chatSessionRepository.findByIdAndUser(sessionId, user)
                .orElseThrow(() -> new RuntimeException("Chat session not found or access denied"));
    }
    
    public void deleteChatSession(Long sessionId, User user) {
        Optional<ChatSession> sessionOpt = chatSessionRepository.findByIdAndUser(sessionId, user);
        if (sessionOpt.isEmpty()) {
            throw new RuntimeException("Chat session not found or access denied");
        }
        
        ChatSession chatSession = sessionOpt.get();
        chatSession.setIsActive(false);
        chatSessionRepository.save(chatSession);
        
        logger.info("Deleted chat session {} by user {}", sessionId, user.getUsername());
    }
    
    public void updateSessionTitle(Long sessionId, String newTitle, User user) {
        Optional<ChatSession> sessionOpt = chatSessionRepository.findByIdAndUser(sessionId, user);
        if (sessionOpt.isEmpty()) {
            throw new RuntimeException("Chat session not found or access denied");
        }
        
        ChatSession chatSession = sessionOpt.get();
        chatSession.setTitle(newTitle);
        chatSessionRepository.save(chatSession);
        
        logger.info("Updated chat session {} title to '{}' by user {}", sessionId, newTitle, user.getUsername());
    }
    
    private String generateSessionTitle(Document document) {
        String filename = document.getOriginalFilename();
        if (filename.contains(".")) {
            filename = filename.substring(0, filename.lastIndexOf("."));
        }
        
        return "Chat with " + filename;
    }
    
    public boolean canCreateChatSession(Long documentId, User user) {
        Optional<Document> documentOpt = documentRepository.findByIdAndUser(documentId, user);
        if (documentOpt.isEmpty()) {
            return false;
        }
        
        Document document = documentOpt.get();
        return document.getProcessingStatus() == Document.ProcessingStatus.COMPLETED;
    }
    
    public Map<String, Object> getChatStatistics(User user) {
        Map<String, Object> stats = new HashMap<>();
        
        long totalSessions = chatSessionRepository.countActiveSessionsByUser(user);
        List<ChatSession> sessions = chatSessionRepository.findActiveSessionsByUser(user);
        
        long totalMessages = sessions.stream()
                .mapToLong(session -> chatMessageRepository.countMessagesBySession(session))
                .sum();
        
        stats.put("totalSessions", totalSessions);
        stats.put("totalMessages", totalMessages);
        stats.put("recentSessions", sessions.stream().limit(5).collect(Collectors.toList()));
        
        return stats;
    }
}
