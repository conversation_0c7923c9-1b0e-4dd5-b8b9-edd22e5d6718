package com.multimodal.ragagent.service;

import java.util.ArrayList;
import java.util.List;

public class QueryResult {
    private List<ResultItem> results = new ArrayList<>();
    
    public List<ResultItem> getResults() {
        return results;
    }
    
    public void setResults(List<ResultItem> results) {
        this.results = results;
    }
    
    public static class ResultItem {
        private String id;
        private String document;
        private double distance;
        private Long documentId;
        private Integer chunkIndex;
        private Integer startPosition;
        private Integer endPosition;
        
        // Getters and setters
        public String getId() {
            return id;
        }
        
        public void setId(String id) {
            this.id = id;
        }
        
        public String getDocument() {
            return document;
        }
        
        public void setDocument(String document) {
            this.document = document;
        }
        
        public double getDistance() {
            return distance;
        }
        
        public void setDistance(double distance) {
            this.distance = distance;
        }
        
        public Long getDocumentId() {
            return documentId;
        }
        
        public void setDocumentId(Long documentId) {
            this.documentId = documentId;
        }
        
        public Integer getChunkIndex() {
            return chunkIndex;
        }
        
        public void setChunkIndex(Integer chunkIndex) {
            this.chunkIndex = chunkIndex;
        }
        
        public Integer getStartPosition() {
            return startPosition;
        }
        
        public void setStartPosition(Integer startPosition) {
            this.startPosition = startPosition;
        }
        
        public Integer getEndPosition() {
            return endPosition;
        }
        
        public void setEndPosition(Integer endPosition) {
            this.endPosition = endPosition;
        }
        
        @Override
        public String toString() {
            return "ResultItem{" +
                    "id='" + id + '\'' +
                    ", document='" + document + '\'' +
                    ", distance=" + distance +
                    ", documentId=" + documentId +
                    ", chunkIndex=" + chunkIndex +
                    ", startPosition=" + startPosition +
                    ", endPosition=" + endPosition +
                    '}';
        }
    }
}
