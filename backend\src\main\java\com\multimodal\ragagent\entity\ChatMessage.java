package com.multimodal.ragagent.entity;

import com.fasterxml.jackson.annotation.JsonIgnore;
import jakarta.persistence.*;
import java.time.LocalDateTime;

@Entity
@Table(name = "chat_messages", indexes = {
    @Index(name = "idx_chat_message_session_id", columnList = "chat_session_id"),
    @Index(name = "idx_chat_message_role", columnList = "role"),
    @Index(name = "idx_chat_message_created_at", columnList = "created_at"),
    @Index(name = "idx_chat_message_session_created", columnList = "chat_session_id, created_at")
})
public class ChatMessage {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "chat_session_id", nullable = false)
    @JsonIgnore
    private ChatSession chatSession;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private MessageRole role;
    
    @Column(columnDefinition = "TEXT", nullable = false)
    private String content;
    
    @Column(name = "created_at", nullable = false)
    private LocalDateTime createdAt;
    
    @Column(name = "token_count")
    private Integer tokenCount;
    
    @Column(name = "response_time_ms")
    private Long responseTimeMs;
    
    @Column(columnDefinition = "TEXT")
    private String metadata; // JSON string for additional data
    
    public enum MessageRole {
        USER, ASSISTANT, SYSTEM
    }
    
    // Constructors
    public ChatMessage() {}
    
    public ChatMessage(ChatSession chatSession, MessageRole role, String content) {
        this.chatSession = chatSession;
        this.role = role;
        this.content = content;
        this.createdAt = LocalDateTime.now();
    }
    
    // Getters and setters
    public Long getId() {
        return id;
    }
    
    public void setId(Long id) {
        this.id = id;
    }
    
    public ChatSession getChatSession() {
        return chatSession;
    }
    
    public void setChatSession(ChatSession chatSession) {
        this.chatSession = chatSession;
    }
    
    public MessageRole getRole() {
        return role;
    }
    
    public void setRole(MessageRole role) {
        this.role = role;
    }
    
    public String getContent() {
        return content;
    }
    
    public void setContent(String content) {
        this.content = content;
    }
    
    public LocalDateTime getCreatedAt() {
        return createdAt;
    }
    
    public void setCreatedAt(LocalDateTime createdAt) {
        this.createdAt = createdAt;
    }
    
    public Integer getTokenCount() {
        return tokenCount;
    }
    
    public void setTokenCount(Integer tokenCount) {
        this.tokenCount = tokenCount;
    }
    
    public Long getResponseTimeMs() {
        return responseTimeMs;
    }
    
    public void setResponseTimeMs(Long responseTimeMs) {
        this.responseTimeMs = responseTimeMs;
    }
    
    public String getMetadata() {
        return metadata;
    }
    
    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
    
    // Helper methods
    public boolean isFromUser() {
        return role == MessageRole.USER;
    }
    
    public boolean isFromAssistant() {
        return role == MessageRole.ASSISTANT;
    }
    
    public boolean isSystemMessage() {
        return role == MessageRole.SYSTEM;
    }
}
