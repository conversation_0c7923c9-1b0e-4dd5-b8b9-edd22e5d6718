package com.multimodal.ragagent.config;

import com.multimodal.ragagent.service.VectorService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.ApplicationArguments;
import org.springframework.boot.ApplicationRunner;
import org.springframework.stereotype.Component;

@Component
public class VectorDatabaseConfig implements ApplicationRunner {
    
    private static final Logger logger = LoggerFactory.getLogger(VectorDatabaseConfig.class);
    
    @Autowired
    private VectorService vectorService;
    
    @Override
    public void run(ApplicationArguments args) throws Exception {
        logger.info("Initializing vector database...");
        
        try {
            vectorService.initializeVectorDatabase();
            
            String status = vectorService.getVectorDatabaseStatus();
            logger.info("Vector database status: {}", status);
            
            if (vectorService.isVectorDatabaseAvailable()) {
                logger.info("Vector database is ready for use");
            } else {
                logger.warn("Vector database is not fully available. Some features may be limited.");
                logger.warn("To enable full RAG functionality:");
                logger.warn("1. Start ChromaDB: docker run -p 8000:8000 chromadb/chroma");
                logger.warn("2. Groq API is configured and ready for AI responses");
            }
            
        } catch (Exception e) {
            logger.error("Failed to initialize vector database: {}", e.getMessage());
            logger.warn("Vector database features will be disabled");
        }
    }
}
