import { create } from 'zustand';
import { notesAPI } from '@/lib/api';

interface Note {
  id: number;
  title: string;
  content: string;
  noteType: 'DETAILED' | 'QUICK_REVISION' | 'MEDIUM_LEVEL';
  status: 'DRAFT' | 'PUBLISHED' | 'ARCHIVED';
  createdAt: string;
  updatedAt: string;
  document: {
    id: number;
    originalFilename: string;
  };
}

interface NotesState {
  notes: Note[];
  currentNote: Note | null;
  isLoading: boolean;
  isGenerating: boolean;
  searchQuery: string;
  filteredNotes: Note[];
  setNotes: (notes: Note[]) => void;
  addNote: (note: Note) => void;
  updateNote: (id: number, updates: Partial<Note>) => void;
  removeNote: (id: number) => void;
  setCurrentNote: (note: Note | null) => void;
  setLoading: (loading: boolean) => void;
  setGenerating: (generating: boolean) => void;
  setSearchQuery: (query: string) => void;
  filterNotes: () => void;
}

export const useNotesStore = create<NotesState>((set, get) => ({
  notes: [],
  currentNote: null,
  isLoading: false,
  isGenerating: false,
  searchQuery: '',
  filteredNotes: [],

  setNotes: (notes: Note[]) => {
    set({ notes, filteredNotes: notes });
  },

  addNote: (note: Note) => {
    set((state) => {
      const newNotes = [note, ...state.notes];
      return {
        notes: newNotes,
        filteredNotes: state.searchQuery
          ? newNotes.filter((n) =>
              n.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
              n.content.toLowerCase().includes(state.searchQuery.toLowerCase())
            )
          : newNotes,
      };
    });
  },

  updateNote: (id: number, updates: Partial<Note>) => {
    set((state) => {
      const updatedNotes = state.notes.map((note) =>
        note.id === id ? { ...note, ...updates } : note
      );
      return {
        notes: updatedNotes,
        filteredNotes: state.searchQuery
          ? updatedNotes.filter((n) =>
              n.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
              n.content.toLowerCase().includes(state.searchQuery.toLowerCase())
            )
          : updatedNotes,
        currentNote:
          state.currentNote?.id === id
            ? { ...state.currentNote, ...updates }
            : state.currentNote,
      };
    });
  },

  removeNote: (id: number) => {
    set((state) => {
      const filteredNotes = state.notes.filter((note) => note.id !== id);
      return {
        notes: filteredNotes,
        filteredNotes: state.searchQuery
          ? filteredNotes.filter((n) =>
              n.title.toLowerCase().includes(state.searchQuery.toLowerCase()) ||
              n.content.toLowerCase().includes(state.searchQuery.toLowerCase())
            )
          : filteredNotes,
        currentNote:
          state.currentNote?.id === id ? null : state.currentNote,
      };
    });
  },

  deleteNote: async (id: number) => {
    try {
      await notesAPI.delete(id);
      get().removeNote(id);
      return true;
    } catch (error) {
      console.error('Failed to delete note:', error);
      throw error;
    }
  },

  setCurrentNote: (note: Note | null) => {
    set({ currentNote: note });
  },

  setLoading: (loading: boolean) => {
    set({ isLoading: loading });
  },

  setGenerating: (generating: boolean) => {
    set({ isGenerating: generating });
  },

  setSearchQuery: (query: string) => {
    set({ searchQuery: query });
    get().filterNotes();
  },

  filterNotes: () => {
    const { notes, searchQuery } = get();
    if (!searchQuery) {
      set({ filteredNotes: notes });
      return;
    }

    const filtered = notes.filter(
      (note) =>
        note.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.content.toLowerCase().includes(searchQuery.toLowerCase()) ||
        note.document.originalFilename.toLowerCase().includes(searchQuery.toLowerCase())
    );

    set({ filteredNotes: filtered });
  },
}));
