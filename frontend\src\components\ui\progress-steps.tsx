import React from 'react';
import { CheckCircle, Circle, ArrowRight } from 'lucide-react';
import { cn } from '@/lib/utils';

interface Step {
  id: string;
  title: string;
  description?: string;
}

interface ProgressStepsProps {
  steps: Step[];
  currentStep: string;
  completedSteps: string[];
  className?: string;
}

export function ProgressSteps({ 
  steps, 
  currentStep, 
  completedSteps, 
  className 
}: ProgressStepsProps) {
  const currentIndex = steps.findIndex(step => step.id === currentStep);
  
  return (
    <div className={cn("flex items-center justify-center space-x-4", className)}>
      {steps.map((step, index) => {
        const isCompleted = completedSteps.includes(step.id);
        const isCurrent = step.id === currentStep;
        const isPast = index < currentIndex;
        
        return (
          <React.Fragment key={step.id}>
            <div className="flex flex-col items-center space-y-2">
              <div className={cn(
                "flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors",
                isCompleted || isPast
                  ? "bg-primary border-primary text-primary-foreground"
                  : isCurrent
                  ? "border-primary text-primary bg-primary/10"
                  : "border-muted-foreground text-muted-foreground"
              )}>
                {isCompleted || isPast ? (
                  <CheckCircle className="w-5 h-5" />
                ) : (
                  <Circle className="w-5 h-5" />
                )}
              </div>
              <div className="text-center">
                <div className={cn(
                  "text-sm font-medium",
                  isCurrent ? "text-primary" : "text-muted-foreground"
                )}>
                  {step.title}
                </div>
                {step.description && (
                  <div className="text-xs text-muted-foreground mt-1">
                    {step.description}
                  </div>
                )}
              </div>
            </div>
            
            {index < steps.length - 1 && (
              <ArrowRight className={cn(
                "w-4 h-4 transition-colors",
                index < currentIndex ? "text-primary" : "text-muted-foreground"
              )} />
            )}
          </React.Fragment>
        );
      })}
    </div>
  );
}
