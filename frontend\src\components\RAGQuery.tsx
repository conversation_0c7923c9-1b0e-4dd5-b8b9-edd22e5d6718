'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Loader2, Search, FileText, MessageSquare } from 'lucide-react';
import { ragAPI } from '@/lib/api';
import { toast } from 'sonner';

interface RAGQueryProps {
  documentId?: number;
  documentName?: string;
}

interface QueryResult {
  answer: string;
  success: boolean;
  sourceChunks?: Array<{
    id: number;
    content: string;
    chunkIndex: number;
    documentName: string;
  }>;
}

export default function RAGQuery({ documentId, documentName }: RAGQueryProps) {
  const [question, setQuestion] = useState('');
  const [loading, setLoading] = useState(false);
  const [result, setResult] = useState<QueryResult | null>(null);

  const handleQuery = async () => {
    if (!question.trim()) {
      toast.error('Please enter a question');
      return;
    }

    setLoading(true);
    try {
      const response = documentId 
        ? await ragAPI.query(documentId, question.trim())
        : await ragAPI.queryAll(question.trim());
      
      setResult(response.data);

      if (!response.data.success) {
        toast.error('Unable to process your query. Please try again.');
      }
    } catch (error) {
      console.error('RAG query failed:', error);
      toast.error('Unable to connect to the system. Please check your connection and try again.');
      setResult(null);
    } finally {
      setLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleQuery();
    }
  };

  return (
    <div className="space-y-6">
      {/* Query Input */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Search className="h-5 w-5" />
            {documentId ? `Ask about ${documentName}` : 'Ask about your documents'}
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Ask a question about the document(s)..."
              value={question}
              onChange={(e) => setQuestion(e.target.value)}
              onKeyPress={handleKeyPress}
              disabled={loading}
              className="flex-1"
            />
            <Button 
              onClick={handleQuery} 
              disabled={loading || !question.trim()}
              className="px-6"
            >
              {loading ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          {documentId && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <FileText className="h-4 w-4" />
              Searching in: {documentName}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Query Result */}
      {result && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Answer
              {result.success && (
                <Badge variant="secondary" className="ml-auto">
                  AI Generated
                </Badge>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="prose prose-sm max-w-none">
              <div className="whitespace-pre-wrap">{result.answer}</div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Source Chunks */}
      {result?.sourceChunks && result.sourceChunks.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Source Information
              <Badge variant="outline" className="ml-auto">
                {result.sourceChunks.length} sources
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {result.sourceChunks.map((chunk, index) => (
              <div
                key={chunk.id}
                className="border rounded-lg p-4 bg-muted/50"
              >
                <div className="flex items-center justify-between mb-2">
                  <div className="flex items-center gap-2 text-sm font-medium">
                    <FileText className="h-4 w-4" />
                    {chunk.documentName}
                  </div>
                  <Badge variant="outline" className="text-xs">
                    Chunk {chunk.chunkIndex + 1}
                  </Badge>
                </div>
                <div className="text-sm text-muted-foreground line-clamp-3">
                  {chunk.content}
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      )}

      {/* Help Text */}
      {!result && (
        <Card className="border-dashed">
          <CardContent className="pt-6">
            <div className="text-center text-muted-foreground">
              <Search className="h-12 w-12 mx-auto mb-4 opacity-50" />
              <h3 className="font-medium mb-2">Ask questions about your documents</h3>
              <p className="text-sm">
                {documentId 
                  ? 'Ask specific questions about this document and get AI-powered answers with source references.'
                  : 'Search across all your documents to find relevant information and get comprehensive answers.'
                }
              </p>
              <div className="mt-4 text-xs space-y-1">
                <p>💡 Try questions like:</p>
                <p>"What are the main points?"</p>
                <p>"Summarize the key findings"</p>
                <p>"What does this document say about [topic]?"</p>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
