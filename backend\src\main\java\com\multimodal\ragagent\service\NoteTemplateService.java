package com.multimodal.ragagent.service;

import org.springframework.stereotype.Service;
import java.util.*;

@Service
public class NoteTemplateService {
    
    public static class NoteTemplate {
        private String id;
        private String name;
        private String description;
        private String category;
        private String template;
        private List<String> features;
        
        public NoteTemplate(String id, String name, String description, String category, String template, List<String> features) {
            this.id = id;
            this.name = name;
            this.description = description;
            this.category = category;
            this.template = template;
            this.features = features;
        }
        
        // Getters
        public String getId() { return id; }
        public String getName() { return name; }
        public String getDescription() { return description; }
        public String getCategory() { return category; }
        public String getTemplate() { return template; }
        public List<String> getFeatures() { return features; }
    }
    
    private final Map<String, NoteTemplate> templates = new HashMap<>();
    
    public NoteTemplateService() {
        initializeTemplates();
    }
    
    private void initializeTemplates() {
        // Basic Templates
        templates.put("detailed", new NoteTemplate(
            "detailed",
            "Detailed Notes",
            "Comprehensive notes with all important details and explanations",
            "Basic",
            "# {title}\n\n## Overview\n{overview}\n\n## Key Points\n{key_points}\n\n## Details\n{details}\n\n## Summary\n{summary}",
            Arrays.asList("Comprehensive coverage", "Detailed explanations", "Structured format")
        ));
        
        templates.put("quick", new NoteTemplate(
            "quick",
            "Quick Revision",
            "Concise bullet points for rapid review",
            "Basic",
            "# {title} - Quick Notes\n\n## Key Facts\n{key_facts}\n\n## Important Points\n{important_points}\n\n## Quick Review\n{quick_review}",
            Arrays.asList("Bullet points", "Concise format", "Fast review")
        ));
        
        templates.put("summary", new NoteTemplate(
            "summary",
            "Executive Summary",
            "High-level overview with key takeaways",
            "Advanced",
            "# Executive Summary: {title}\n\n## Purpose\n{purpose}\n\n## Key Findings\n{key_findings}\n\n## Conclusions\n{conclusions}\n\n## Action Items\n{action_items}",
            Arrays.asList("Executive level", "Key insights", "Actionable conclusions")
        ));
        
        // Study Aid Templates
        templates.put("flashcards", new NoteTemplate(
            "flashcards",
            "Flashcards",
            "Question-answer pairs for active recall",
            "Study Aids",
            "# Flashcards: {title}\n\n{flashcard_pairs}",
            Arrays.asList("Q&A format", "Active recall", "Memory reinforcement")
        ));
        
        templates.put("qna", new NoteTemplate(
            "qna",
            "Q&A Study Guide",
            "Comprehensive questions and answers for exam prep",
            "Study Aids",
            "# Study Guide: {title}\n\n## Factual Questions\n{factual_questions}\n\n## Conceptual Questions\n{conceptual_questions}\n\n## Analytical Questions\n{analytical_questions}",
            Arrays.asList("Multiple question types", "Exam preparation", "Comprehensive coverage")
        ));
        
        templates.put("study_guide", new NoteTemplate(
            "study_guide",
            "Study Guide",
            "Complete study material with exercises and tips",
            "Study Aids",
            "# Study Guide: {title}\n\n## Learning Objectives\n{objectives}\n\n## Key Concepts\n{concepts}\n\n## Practice Questions\n{practice}\n\n## Study Tips\n{tips}\n\n## Additional Resources\n{resources}",
            Arrays.asList("Learning objectives", "Practice questions", "Study strategies", "Resource links")
        ));
        
        // Advanced Templates
        templates.put("outline", new NoteTemplate(
            "outline",
            "Structured Outline",
            "Hierarchical organization of topics",
            "Advanced",
            "# Outline: {title}\n\n{hierarchical_structure}",
            Arrays.asList("Hierarchical structure", "Logical organization", "Clear numbering")
        ));
        
        templates.put("mindmap", new NoteTemplate(
            "mindmap",
            "Mind Map",
            "Visual concept mapping with relationships",
            "Visual",
            "# Mind Map: {title}\n\n```\n{mindmap_structure}\n```\n\n## Concept Relationships\n{relationships}\n\n## Key Connections\n{connections}",
            Arrays.asList("Visual structure", "Concept relationships", "Connection mapping")
        ));
        
        templates.put("key_points", new NoteTemplate(
            "key_points",
            "Key Points",
            "Essential facts and highlights",
            "Advanced",
            "# Key Points: {title}\n\n## Critical Information\n{critical_info}\n\n## Important Facts\n{important_facts}\n\n## Key Takeaways\n{takeaways}",
            Arrays.asList("Essential information", "Prioritized content", "Highlight format")
        ));
    }
    
    public List<NoteTemplate> getAllTemplates() {
        return new ArrayList<>(templates.values());
    }
    
    public List<NoteTemplate> getTemplatesByCategory(String category) {
        return templates.values().stream()
            .filter(template -> template.getCategory().equals(category))
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    public NoteTemplate getTemplate(String id) {
        return templates.get(id);
    }
    
    public List<String> getCategories() {
        return templates.values().stream()
            .map(NoteTemplate::getCategory)
            .distinct()
            .sorted()
            .collect(ArrayList::new, ArrayList::add, ArrayList::addAll);
    }
    
    public String applyTemplate(String templateId, Map<String, String> variables) {
        NoteTemplate template = templates.get(templateId);
        if (template == null) {
            return "Template not found: " + templateId;
        }
        
        String result = template.getTemplate();
        
        // Replace variables in template
        for (Map.Entry<String, String> entry : variables.entrySet()) {
            String placeholder = "{" + entry.getKey() + "}";
            result = result.replace(placeholder, entry.getValue() != null ? entry.getValue() : "");
        }
        
        // Clean up any remaining placeholders
        result = result.replaceAll("\\{[^}]+\\}", "[Content to be generated]");
        
        return result;
    }
    
    public Map<String, Object> getTemplateInfo(String templateId) {
        NoteTemplate template = templates.get(templateId);
        if (template == null) {
            return null;
        }
        
        Map<String, Object> info = new HashMap<>();
        info.put("id", template.getId());
        info.put("name", template.getName());
        info.put("description", template.getDescription());
        info.put("category", template.getCategory());
        info.put("features", template.getFeatures());
        
        return info;
    }
}
